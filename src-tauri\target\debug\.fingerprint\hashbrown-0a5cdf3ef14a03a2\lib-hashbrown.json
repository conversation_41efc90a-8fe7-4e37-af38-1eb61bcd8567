{"rustc": 1842507548689473721, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2225463790103693989, "path": 9623890445795218302, "deps": [[966925859616469517, "ahash", false, 12556814651952419371], [9150530836556604396, "allocator_api2", false, 246160815297235276]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-0a5cdf3ef14a03a2\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}