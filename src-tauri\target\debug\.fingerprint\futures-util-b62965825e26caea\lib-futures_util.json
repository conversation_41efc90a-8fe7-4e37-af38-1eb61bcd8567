{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8113656176662020586, "path": 15389663814530670383, "deps": [[5103565458935487, "futures_io", false, 5192599958538466560], [1615478164327904835, "pin_utils", false, 8274102835293351996], [1906322745568073236, "pin_project_lite", false, 16371255440629486796], [5451793922601807560, "slab", false, 11084205599844982025], [7013762810557009322, "futures_sink", false, 14578494298012690386], [7620660491849607393, "futures_core", false, 235090336762385746], [15932120279885307830, "memchr", false, 8878295398709515274], [16240732885093539806, "futures_task", false, 6774818387472760842]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-b62965825e26caea\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}