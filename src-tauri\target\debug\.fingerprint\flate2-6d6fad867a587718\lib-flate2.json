{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 731671357045034073, "deps": [[5466618496199522463, "crc32fast", false, 7157255337215467225], [7636735136738807108, "miniz_oxide", false, 1422684142809600974]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-6d6fad867a587718\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}