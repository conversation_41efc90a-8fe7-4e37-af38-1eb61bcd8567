use sqlx::{SqlitePool, Row};
use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::fs;
use tauri::AppHandle;
use crate::browser::Tab;
use crate::intelligence::SmartBookmark;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HistoryEntry {
    pub id: i64,
    pub url: String,
    pub title: String,
    pub visit_count: u32,
    pub last_visited: i64,
    pub time_spent: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FrequentSite {
    pub url: String,
    pub visit_count: u32,
    pub average_time_spent: u64,
    pub last_visited: i64,
}

/// Database path handler for Tauri v1 (Desktop only)
pub struct DatabasePath;

impl DatabasePath {
    /// Get database path for Tauri v1 app
    pub fn get_db_path(app_handle: &tauri::AppHandle) -> Result<PathBuf> {
        // Use Tauri's built-in path resolver
        let app_dir = app_handle
            .path_resolver()
            .app_data_dir()
            .ok_or_else(|| anyhow::anyhow!("Failed to resolve app data directory"))?;

        // Create directory if it doesn't exist
        fs::create_dir_all(&app_dir)
            .with_context(|| format!("Failed to create app directory: {:?}", app_dir))?;

        let db_path = app_dir.join("browser.db");

        // Log the path for debugging
        println!("Database path: {:?}", db_path);

        Ok(db_path)
    }

    /// Get platform-specific data directory
    fn get_platform_data_dir() -> Result<PathBuf> {
        #[cfg(target_os = "windows")]
        {
            if let Ok(local_app_data) = std::env::var("LOCALAPPDATA") {
                return Ok(PathBuf::from(local_app_data).join("memori-browser"));
            }
            if let Ok(app_data) = std::env::var("APPDATA") {
                return Ok(PathBuf::from(app_data).join("memori-browser"));
            }
        }

        // Fallback
        dirs::data_local_dir()
            .map(|d| d.join("memori-browser"))
            .ok_or_else(|| anyhow::anyhow!("Could not determine data directory"))
    }
}

pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    pub async fn new_with_app_handle(app_handle: &tauri::AppHandle) -> Result<Self> {
        let db_path = DatabasePath::get_db_path(app_handle)?;

        let database_url = format!("sqlite:{}", db_path.display());
        let pool = SqlitePool::connect(&database_url).await
            .with_context(|| format!("Failed to connect to database at {:?}", db_path))?;

        let manager = Self { pool };
        manager.initialize_schema().await?;

        println!("✅ Database initialized successfully at {:?}", db_path);
        Ok(manager)
    }

    pub async fn new() -> Result<Self> {
        let db_path = Self::get_database_path()?;

        // Ensure the directory exists
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let database_url = format!("sqlite:{}", db_path.display());
        let pool = SqlitePool::connect(&database_url).await?;

        let manager = Self { pool };
        manager.initialize_schema().await?;

        Ok(manager)
    }

    fn get_database_path() -> Result<PathBuf> {
        // Use current directory for now to avoid permissions issues
        let mut path = std::env::current_dir()?;
        path.push("browser.db");

        Ok(path)
    }

    async fn initialize_schema(&self) -> Result<()> {
        // Create tabs table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tabs (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                url TEXT NOT NULL,
                favicon TEXT,
                is_active BOOLEAN NOT NULL DEFAULT FALSE,
                is_loading BOOLEAN NOT NULL DEFAULT FALSE,
                is_playing_audio BOOLEAN NOT NULL DEFAULT FALSE,
                last_accessed INTEGER NOT NULL,
                bookmark_suggested BOOLEAN NOT NULL DEFAULT FALSE,
                visit_count INTEGER NOT NULL DEFAULT 1,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create browsing history table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL,
                title TEXT NOT NULL,
                visit_count INTEGER NOT NULL DEFAULT 1,
                last_visited INTEGER NOT NULL,
                time_spent INTEGER NOT NULL DEFAULT 0,
                UNIQUE(url)
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create bookmarks table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS bookmarks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url TEXT NOT NULL UNIQUE,
                title TEXT NOT NULL,
                visit_count INTEGER NOT NULL DEFAULT 0,
                time_spent INTEGER NOT NULL DEFAULT 0,
                bookmark_score REAL NOT NULL DEFAULT 0.0,
                suggested_at INTEGER NOT NULL,
                auto_bookmarked BOOLEAN NOT NULL DEFAULT FALSE,
                created_at TEXT NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create search queries table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS search_queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query TEXT NOT NULL,
                results_count INTEGER NOT NULL DEFAULT 0,
                clicked_result TEXT,
                timestamp INTEGER NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create audio events table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS audio_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tab_id TEXT NOT NULL,
                is_playing BOOLEAN NOT NULL,
                volume_level REAL NOT NULL DEFAULT 0.0,
                audio_type TEXT NOT NULL DEFAULT 'Unknown',
                detected_at INTEGER NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create indexes for better performance
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tabs_last_accessed ON tabs(last_accessed)")
            .execute(&self.pool)
            .await?;
        
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_history_last_visited ON history(last_visited)")
            .execute(&self.pool)
            .await?;
        
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_history_visit_count ON history(visit_count)")
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    // Tab operations
    pub async fn save_tab(&self, tab: &Tab) -> Result<()> {
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO tabs 
            (id, title, url, favicon, is_active, is_loading, is_playing_audio, 
             last_accessed, bookmark_suggested, visit_count, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&tab.id)
        .bind(&tab.title)
        .bind(&tab.url)
        .bind(&tab.favicon)
        .bind(tab.is_active)
        .bind(tab.is_loading)
        .bind(tab.is_playing_audio)
        .bind(tab.last_accessed)
        .bind(tab.bookmark_suggested)
        .bind(tab.visit_count)
        .bind(tab.created_at.to_rfc3339())
        .bind(tab.updated_at.to_rfc3339())
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_all_tabs(&self) -> Result<Vec<Tab>> {
        let rows = sqlx::query("SELECT * FROM tabs ORDER BY last_accessed DESC")
            .fetch_all(&self.pool)
            .await?;

        let mut tabs = Vec::new();
        for row in rows {
            tabs.push(Tab {
                id: row.get("id"),
                title: row.get("title"),
                url: row.get("url"),
                favicon: row.get("favicon"),
                is_active: row.get("is_active"),
                is_loading: row.get("is_loading"),
                is_playing_audio: row.get("is_playing_audio"),
                last_accessed: row.get("last_accessed"),
                bookmark_suggested: row.get("bookmark_suggested"),
                visit_count: row.get("visit_count"),
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("created_at"))?.with_timezone(&chrono::Utc),
                updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<String, _>("updated_at"))?.with_timezone(&chrono::Utc),
            });
        }

        Ok(tabs)
    }

    pub async fn delete_tab(&self, tab_id: &str) -> Result<()> {
        sqlx::query("DELETE FROM tabs WHERE id = ?")
            .bind(tab_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    // History operations
    pub async fn add_to_history(&self, url: &str, title: &str) -> Result<()> {
        let now = chrono::Utc::now().timestamp_millis();
        
        sqlx::query(
            r#"
            INSERT INTO history (url, title, visit_count, last_visited, time_spent)
            VALUES (?, ?, 1, ?, 0)
            ON CONFLICT(url) DO UPDATE SET
                visit_count = visit_count + 1,
                last_visited = ?,
                title = ?
            "#,
        )
        .bind(url)
        .bind(title)
        .bind(now)
        .bind(now)
        .bind(title)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_browsing_history(&self, limit: i32) -> Result<Vec<HistoryEntry>> {
        let rows = sqlx::query(
            "SELECT * FROM history ORDER BY last_visited DESC LIMIT ?"
        )
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;

        let mut history = Vec::new();
        for row in rows {
            history.push(HistoryEntry {
                id: row.get("id"),
                url: row.get("url"),
                title: row.get("title"),
                visit_count: row.get("visit_count"),
                last_visited: row.get("last_visited"),
                time_spent: row.get::<i64, _>("time_spent") as u64,
            });
        }

        Ok(history)
    }

    pub async fn get_frequent_sites(&self, limit: i32) -> Result<Vec<FrequentSite>> {
        let rows = sqlx::query(
            r#"
            SELECT url, visit_count, time_spent, last_visited 
            FROM history 
            WHERE visit_count > 1 
            ORDER BY visit_count DESC, last_visited DESC 
            LIMIT ?
            "#
        )
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;

        let mut sites = Vec::new();
        for row in rows {
            let visit_count: u32 = row.get("visit_count");
            let time_spent: u64 = row.get::<i64, _>("time_spent") as u64;
            
            sites.push(FrequentSite {
                url: row.get("url"),
                visit_count,
                average_time_spent: if visit_count > 0 { time_spent / visit_count as u64 } else { 0 },
                last_visited: row.get("last_visited"),
            });
        }

        Ok(sites)
    }

    // Bookmark operations
    pub async fn save_smart_bookmark(&self, bookmark: &SmartBookmark) -> Result<()> {
        let now = chrono::Utc::now().to_rfc3339();
        
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO bookmarks 
            (url, title, visit_count, time_spent, bookmark_score, suggested_at, auto_bookmarked, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&bookmark.url)
        .bind(&bookmark.title)
        .bind(bookmark.visit_count)
        .bind(bookmark.time_spent as i64)
        .bind(bookmark.bookmark_score)
        .bind(bookmark.suggested_at)
        .bind(bookmark.auto_bookmarked)
        .bind(now)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_bookmark_suggestions(&self) -> Result<Vec<SmartBookmark>> {
        let rows = sqlx::query(
            "SELECT * FROM bookmarks WHERE bookmark_score > 0.5 ORDER BY bookmark_score DESC LIMIT 10"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut bookmarks = Vec::new();
        for row in rows {
            bookmarks.push(SmartBookmark {
                url: row.get("url"),
                title: row.get("title"),
                visit_count: row.get("visit_count"),
                time_spent: row.get::<i64, _>("time_spent") as u64,
                bookmark_score: row.get("bookmark_score"),
                suggested_at: row.get("suggested_at"),
                auto_bookmarked: row.get("auto_bookmarked"),
            });
        }

        Ok(bookmarks)
    }

    // Usage tracking
    pub async fn update_site_usage(&self, url: &str, time_spent: u64) -> Result<()> {
        sqlx::query(
            "UPDATE history SET time_spent = time_spent + ? WHERE url = ?"
        )
        .bind(time_spent as i64)
        .bind(url)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // Search tracking
    pub async fn log_search_query(&self, query: &str, results_count: i32, clicked_result: Option<&str>) -> Result<()> {
        let now = chrono::Utc::now().timestamp_millis();
        
        sqlx::query(
            "INSERT INTO search_queries (query, results_count, clicked_result, timestamp) VALUES (?, ?, ?, ?)"
        )
        .bind(query)
        .bind(results_count)
        .bind(clicked_result)
        .bind(now)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // Cleanup operations
    pub async fn cleanup_old_data(&self, days_to_keep: i32) -> Result<()> {
        let cutoff = chrono::Utc::now().timestamp_millis() - (days_to_keep as i64 * 24 * 60 * 60 * 1000);
        
        // Clean old history entries with low visit counts
        sqlx::query(
            "DELETE FROM history WHERE last_visited < ? AND visit_count < 3"
        )
        .bind(cutoff)
        .execute(&self.pool)
        .await?;

        // Clean old search queries
        sqlx::query(
            "DELETE FROM search_queries WHERE timestamp < ?"
        )
        .bind(cutoff)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}
