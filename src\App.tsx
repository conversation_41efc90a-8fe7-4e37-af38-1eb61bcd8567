import React, { useState, useCallback } from 'react';
import { ArrowLeft, ArrowRight, RotateCcw, Home, Search, Mic, Settings, Shield, Lock } from 'lucide-react';
import OptimizedTabBar from './components/OptimizedTabBar';
// import { BrowserView } from './components/BrowserView';
import { useBrowserStore } from './stores/browserStore';

const App: React.FC = () => {
  const [addressBarValue, setAddressBarValue] = useState('');
  const [isVoiceActive, setIsVoiceActive] = useState(false);

  const {
    tabs,
    activeTabId,
    createTab,
    navigateToUrl
  } = useBrowserStore();

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  const handleNavigate = useCallback((url: string) => {
    if (activeTabId) {
      navigateToUrl(url);
    } else {
      createTab(url);
    }
  }, [activeTabId, navigateToUrl, createTab]);

  const handleAddressBarSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (addressBarValue.trim()) {
      let url = addressBarValue.trim();

      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        // Check if it looks like a domain
        if (url.includes('.') && !url.includes(' ')) {
          url = 'https://' + url;
        } else {
          // Treat as search query
          url = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
        }
      }

      handleNavigate(url);
    }
  }, [addressBarValue, handleNavigate]);

  const handleVoiceSearch = useCallback(() => {
    setIsVoiceActive(!isVoiceActive);
    // TODO: Implement voice search
  }, [isVoiceActive]);

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Tab Bar */}
      <OptimizedTabBar />

      {/* Navigation Bar - Chrome-like */}
      <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 border-b border-gray-200">
        {/* Navigation Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => console.log('Back - TODO: implement')}
            disabled={true}
            className="p-2 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Back"
          >
            <ArrowLeft className="w-4 h-4 text-gray-600" />
          </button>
          <button
            onClick={() => console.log('Forward - TODO: implement')}
            disabled={true}
            className="p-2 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Forward"
          >
            <ArrowRight className="w-4 h-4 text-gray-600" />
          </button>
          <button
            onClick={() => activeTab && handleNavigate(activeTab.url)}
            className="p-2 rounded-full hover:bg-gray-200"
            title="Refresh"
          >
            <RotateCcw className="w-4 h-4 text-gray-600" />
          </button>
          <button
            onClick={() => handleNavigate('https://google.com')}
            className="p-2 rounded-full hover:bg-gray-200"
            title="Home"
          >
            <Home className="w-4 h-4 text-gray-600" />
          </button>
        </div>

        {/* Address Bar */}
        <form onSubmit={handleAddressBarSubmit} className="flex-1 flex items-center">
          <div className="flex-1 flex items-center bg-white border border-gray-300 rounded-full px-4 py-2 focus-within:border-blue-500 focus-within:shadow-sm">
            {/* Security indicator */}
            <div className="flex items-center mr-2">
              {activeTab?.url?.startsWith('https://') ? (
                <Lock className="w-4 h-4 text-green-600" title="Secure" />
              ) : (
                <Shield className="w-4 h-4 text-gray-400" title="Not secure" />
              )}
            </div>

            <input
              type="text"
              value={addressBarValue || activeTab?.url || ''}
              onChange={(e) => setAddressBarValue(e.target.value)}
              placeholder="Search Google or type a URL"
              className="flex-1 outline-none text-sm text-gray-900 placeholder-gray-500"
            />

            <button
              type="button"
              onClick={handleVoiceSearch}
              className={`ml-2 p-1 rounded ${isVoiceActive ? 'text-red-500' : 'text-gray-400 hover:text-gray-600'}`}
              title="Voice search"
            >
              <Mic className="w-4 h-4" />
            </button>
          </div>
        </form>

        {/* Browser Controls */}
        <div className="flex items-center gap-1">
          <button
            className="p-2 rounded-full hover:bg-gray-200"
            title="Settings"
          >
            <Settings className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Main Browser Content */}
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-6xl mb-4">🧠</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-4">Memori Browser</h1>
          <p className="text-gray-600 mb-8">Chrome-like interface with AI-enhanced browsing</p>
          <div className="text-sm text-gray-500">
            ✅ Backend running • ✅ Tab system active • ✅ Navigation ready
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
