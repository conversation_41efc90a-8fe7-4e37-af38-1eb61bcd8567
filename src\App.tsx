import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { useBrowserStore } from './stores/browserStore';
import OptimizedTabBar from './components/OptimizedTabBar';
import { ArrowLeft, ArrowRight, RotateCcw, Search, Mic, Home, Shield, Zap } from 'lucide-react';

interface NavigationState {
  canGoBack: boolean;
  canGoForward: boolean;
  isLoading: boolean;
}

const App: React.FC = () => {
  const [currentUrl, setCurrentUrl] = useState('');
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    canGoBack: false,
    canGoForward: false,
    isLoading: false
  });

  const {
    tabs,
    activeTabId,
    createTab,
    navigateToUrl,
    goBack,
    goForward,
    refreshTab,
    searchQuery,
    setSearchQuery
  } = useBrowserStore();

  // Memoized active tab
  const activeTab = useMemo(() =>
    tabs.find(tab => tab.id === activeTabId),
    [tabs, activeTabId]
  );

  // Test Tauri API connection on startup
  useEffect(() => {
    const testConnection = async () => {
      try {
        const info = await invoke('get_browser_info');
        console.log('✅ Tauri API connected:', info);
      } catch (error) {
        console.error('❌ Tauri API connection failed:', error);
      }
    };
    testConnection();
  }, []);

  // Update navigation state when active tab changes
  useEffect(() => {
    if (activeTab) {
      setNavigationState({
        canGoBack: activeTab.can_go_back || false,
        canGoForward: activeTab.can_go_forward || false,
        isLoading: activeTab.is_loading || false
      });
      setCurrentUrl(activeTab.url || '');
    }
  }, [activeTab]);

  const handleNavigate = async () => {
    if (!currentUrl.trim()) return;

    setLoading(true);
    try {
      // Create new tab using the store
      await createTab(currentUrl);
      console.log('🌐 Created new tab for:', currentUrl);
    } catch (error) {
      console.error('Navigation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNavigate();
    }
  };

  const quickNavigate = (url: string) => {
    setCurrentUrl(url);
    setTimeout(() => handleNavigate(), 100);
  };

  const handleVoiceSearch = async () => {
    setIsVoiceActive(!isVoiceActive);
    if (!isVoiceActive) {
      try {
        const result = await invoke('start_voice_recognition');
        console.log('Voice recognition result:', result);
        setCurrentUrl(result as string);
      } catch (error) {
        console.error('Voice recognition failed:', error);
      }
    }
  };

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Header with Navigation and Address Bar */}
      <div className="bg-gray-800 border-b border-gray-700">
        {/* Tab Bar */}
        <div className="flex items-center px-4 py-2 border-b border-gray-700">
          <div className="flex items-center gap-1 flex-1 overflow-x-auto">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className={`flex items-center gap-2 px-3 py-2 rounded-t-lg cursor-pointer min-w-0 max-w-48 ${
                  tab.id === activeTabId 
                    ? 'bg-gray-700 border-b-2 border-blue-500' 
                    : 'bg-gray-800 hover:bg-gray-700'
                }`}
                onClick={() => switchTab(tab.id)}
              >
                {audioTabs.includes(tab.id) && (
                  <Volume2 className="w-3 h-3 text-blue-400 flex-shrink-0" />
                )}
                <span className="truncate text-sm">{tab.title || 'New Tab'}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeTab(tab.id);
                  }}
                  className="p-1 hover:bg-gray-600 rounded"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
          <button
            onClick={() => createTab()}
            className="p-2 hover:bg-gray-700 rounded ml-2"
            title="New Tab"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        {/* Navigation Bar */}
        <div className="flex items-center gap-4 p-4">
          {/* Navigation buttons */}
          <div className="flex gap-2">
            <button className="p-2 bg-gray-700 rounded hover:bg-gray-600 disabled:opacity-50">
              <ArrowLeft className="w-4 h-4" />
            </button>
            <button className="p-2 bg-gray-700 rounded hover:bg-gray-600 disabled:opacity-50">
              <ArrowRight className="w-4 h-4" />
            </button>
            <button className="p-2 bg-gray-700 rounded hover:bg-gray-600">
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>

          {/* Address bar */}
          <div className="flex-1 flex items-center gap-2">
            <div className="flex-1 relative">
              <input
                type="text"
                value={currentUrl}
                onChange={(e) => setCurrentUrl(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter URL or search..."
                className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
            
            <button
              onClick={handleVoiceSearch}
              className={`p-2 rounded-lg ${
                isVoiceActive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
              title="Voice Search"
            >
              <Mic className="w-4 h-4" />
            </button>

            <button
              onClick={handleNavigate}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg"
            >
              {loading ? 'Loading...' : 'Go'}
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Browser view */}
        <div className="flex-1 bg-white">
          {tabs.length === 0 || !activeTab ? (
            <div className="h-full flex flex-col items-center justify-center text-gray-800">
              <div className="text-6xl mb-4">🧠</div>
              <h1 className="text-3xl font-bold mb-4">Memori Browser</h1>
              <p className="text-gray-600 mb-8">AI-Enhanced Browsing Experience</p>
              
              <div className="flex gap-4 mb-8">
                <button
                  onClick={() => quickNavigate('https://example.com')}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  📄 Example.com
                </button>
                <button
                  onClick={() => quickNavigate('https://httpbin.org')}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  🔧 HTTPBin
                </button>
                <button
                  onClick={() => quickNavigate('https://duckduckgo.com')}
                  className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
                >
                  🔍 DuckDuckGo
                </button>
              </div>

              <div className="grid grid-cols-2 gap-4 max-w-2xl">
                <div className="p-4 bg-gray-100 rounded-lg text-center">
                  <div className="text-2xl mb-2">🤖</div>
                  <h3 className="font-bold">AI Assistant</h3>
                  <p className="text-sm text-gray-600">Built-in AI for browsing help</p>
                </div>
                <div className="p-4 bg-gray-100 rounded-lg text-center">
                  <div className="text-2xl mb-2">⚡</div>
                  <h3 className="font-bold">Lightning Fast</h3>
                  <p className="text-sm text-gray-600">Rust-powered performance</p>
                </div>
                <div className="p-4 bg-gray-100 rounded-lg text-center">
                  <div className="text-2xl mb-2">🔒</div>
                  <h3 className="font-bold">Secure</h3>
                  <p className="text-sm text-gray-600">Advanced privacy protection</p>
                </div>
                <div className="p-4 bg-gray-100 rounded-lg text-center">
                  <div className="text-2xl mb-2">💾</div>
                  <h3 className="font-bold">Smart Memory</h3>
                  <p className="text-sm text-gray-600">Remembers your preferences</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full p-4">
              <div className="bg-gray-100 rounded-lg p-8 text-center text-gray-800">
                <div className="text-4xl mb-4">🌐</div>
                <h2 className="text-xl font-bold mb-2">Tab Active: {activeTab.title}</h2>
                <p className="text-gray-600 mb-4">
                  URL: {activeTab.url}
                </p>
                <div className="bg-green-100 border border-green-300 rounded-lg p-4">
                  <p className="text-green-800">
                    ✅ <strong>Real Browser Navigation:</strong> This site is opening in a native webview!
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* AI Sidebar */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
          <h3 className="text-lg font-bold mb-4">🤖 AI Assistant</h3>
          <div className="space-y-2">
            <button className="w-full p-3 bg-gray-700 rounded hover:bg-gray-600 text-left">
              📄 Summarize Page
            </button>
            <button className="w-full p-3 bg-gray-700 rounded hover:bg-gray-600 text-left">
              💡 Explain Selection
            </button>
            <button className="w-full p-3 bg-gray-700 rounded hover:bg-gray-600 text-left">
              🌐 Translate Page
            </button>
            <button className="w-full p-3 bg-gray-700 rounded hover:bg-gray-600 text-left">
              ✅ Fact Check
            </button>
          </div>

          {/* Memory Panel */}
          <div className="mt-6">
            <h4 className="font-bold mb-2">💾 Smart Memory</h4>
            <div className="space-y-1 text-sm">
              <div className="p-2 bg-gray-700 rounded">
                <div className="text-gray-300">Recent searches</div>
                <div className="text-gray-400 text-xs">AI-powered suggestions</div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <div className="text-gray-300">Bookmarks</div>
                <div className="text-gray-400 text-xs">Smart organization</div>
              </div>
            </div>
          </div>

          {/* Active tabs info */}
          {tabs.length > 0 && (
            <div className="mt-6">
              <h4 className="font-bold mb-2">Active Tabs ({tabs.length})</h4>
              <div className="space-y-1">
                {tabs.slice(0, 5).map((tab) => (
                  <div key={tab.id} className="p-2 bg-gray-700 rounded text-sm">
                    <div className="font-medium truncate">{tab.title || 'Loading...'}</div>
                    <div className="text-gray-400 text-xs truncate">{tab.url}</div>
                  </div>
                ))}
                {tabs.length > 5 && (
                  <div className="text-gray-400 text-xs text-center">
                    +{tabs.length - 5} more tabs
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Status bar */}
      <div className="bg-gray-800 border-t border-gray-700 px-4 py-2 text-sm text-gray-400">
        <div className="flex justify-between">
          <span>Ready • {tabs.length} tabs</span>
          <span>Memori Browser v1.0.0 - React + Tauri</span>
        </div>
      </div>
    </div>
  );
}

export default App;
