import React, { useEffect, useState } from "react";
import { useBrowserStore } from "../stores/browserStore";
import { invoke } from "@tauri-apps/api/tauri";

export function BrowserView() {
  const { tabs, activeTabId } = useBrowserStore();
  const [webviewReady, setWebviewReady] = useState(false);

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  // Initialize webview when active tab changes
  useEffect(() => {
    const initializeWebview = async () => {
      if (activeTab && activeTab.url) {
        try {
          console.log('🌐 Initializing webview for tab:', activeTab.id, 'URL:', activeTab.url);

          // Tell backend to show this tab's webview
          await invoke('show_tab_webview', { tab_id: activeTab.id });
          setWebviewReady(true);

        } catch (error) {
          console.error('Failed to initialize webview:', error);
          setWebviewReady(false);
        }
      } else {
        setWebviewReady(false);
      }
    };

    initializeWebview();
  }, [activeTab]);

  return (
    <div className="flex-1 bg-white">
      {!activeTab ? (
        // No active tab - show welcome screen
        <div className="h-full flex items-center justify-center bg-gray-50">
          <div className="text-center text-gray-600">
            <div className="text-6xl mb-4">🧠</div>
            <h1 className="text-2xl font-bold mb-2">Welcome to Memori Browser</h1>
            <p className="text-gray-500 mb-4">Create a new tab or navigate to a website to get started</p>
            <div className="text-sm text-gray-400">
              The webview will open in a separate window for full browser functionality
            </div>
          </div>
        </div>
      ) : webviewReady ? (
        // Webview is ready - show status
        <div className="h-full flex items-center justify-center bg-gray-50">
          <div className="text-center text-gray-700">
            <div className="text-4xl mb-4">🌐</div>
            <h2 className="text-xl font-bold mb-2">Webview Active</h2>
            <p className="text-gray-600 mb-2">
              <strong>{activeTab.title || 'Loading...'}</strong>
            </p>
            <p className="text-sm text-gray-500 mb-4">
              {activeTab.url}
            </p>
            <div className="bg-green-100 border border-green-300 rounded-lg p-4 max-w-md">
              <p className="text-green-800 text-sm">
                ✅ <strong>Website is open in a separate window</strong><br/>
                This provides full browser functionality with proper isolation and security.
              </p>
            </div>
          </div>
        </div>
      ) : (
        // Loading webview
        <div className="h-full flex items-center justify-center bg-gray-50">
          <div className="text-center text-gray-600">
            <div className="animate-spin text-4xl mb-4">⚡</div>
            <h2 className="text-xl font-bold mb-2">Loading Webview...</h2>
            <p className="text-gray-500">
              Preparing to load: {activeTab.url}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
