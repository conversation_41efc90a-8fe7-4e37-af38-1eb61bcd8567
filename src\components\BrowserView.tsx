import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, RotateCcw, Home } from "lucide-react";

export function BrowserView() {
  const currentUrl = "https://google.com";

  const handleBack = () => {
    // TODO: Implement navigation
    console.log("Going back");
  };

  const handleForward = () => {
    // TODO: Implement navigation
    console.log("Going forward");
  };

  const handleRefresh = () => {
    // TODO: Implement refresh
    console.log("Refreshing");
  };

  const handleHome = () => {
    // TODO: Implement home navigation
    console.log("Going home");
  };

  return (
    <div className="flex flex-col h-full">
      {/* Navigation Bar */}
      <div className="flex items-center gap-2 p-2 bg-gray-800 border-b border-gray-700">
        <button
          onClick={handleBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        <button
          onClick={handleForward}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <ArrowRight className="w-4 h-4" />
        </button>
        <button
          onClick={handleRefresh}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
        </button>
        <button
          onClick={handleHome}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <Home className="w-4 h-4" />
        </button>
        
        {/* URL Bar */}
        <div className="flex-1 mx-4">
          <input
            type="text"
            value={currentUrl}
            className="w-full px-3 py-1 bg-gray-900 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-memori-500"
            readOnly
          />
        </div>
      </div>

      {/* Webview Container */}
      <div className="flex-1 bg-white">
        {/* This will be replaced with actual webview */}
        <div className="h-full flex items-center justify-center bg-gray-100">
          <div className="text-center text-gray-600">
            <div className="text-6xl mb-4"></div>
            <h1 className="text-2xl font-bold mb-2">Welcome to Memori</h1>
            <p className="text-gray-500">The AI-enhanced browser that remembers everything</p>
          </div>
        </div>
      </div>
    </div>
  );
}
