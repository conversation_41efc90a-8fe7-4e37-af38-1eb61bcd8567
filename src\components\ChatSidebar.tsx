import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, X, Minimize2, Maximize2, Mic, Copy, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useBrowserStore } from '../stores/browserStore';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
}

const ChatSidebar: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      content: "Hi! I'm your AI browsing assistant. I can help you with:\n\n🔍 **Search & Find**\n• Search tabs, history, bookmarks\n• Find specific content\n\n📑 **Tab Management**\n• Organize and close tabs\n• Find audio-playing tabs\n\n🔖 **Smart Bookmarks**\n• Auto-suggest bookmarks\n• Organize your saves\n\n📝 **Content Help**\n• Summarize pages\n• Translate text\n• Explain code\n\nJust ask me anything!",
      role: 'assistant',
      timestamp: Date.now() - 1000
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { 
    isChatSidebarOpen, 
    toggleChatSidebar, 
    sendChatMessage,
    isVoiceSearchActive,
    startVoiceRecognition 
  } = useBrowserStore();

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      role: 'user',
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await sendChatMessage(inputMessage);
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response,
        role: 'assistant',
        timestamp: Date.now()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "Sorry, I encountered an error. Please try again.",
        role: 'assistant',
        timestamp: Date.now()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceInput = async () => {
    try {
      const result = await startVoiceRecognition();
      setInputMessage(result);
    } catch (error) {
      console.error('Voice input failed:', error);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const quickActions = [
    { text: "What tabs are playing audio?", icon: "🔊" },
    { text: "Close duplicate tabs", icon: "📑" },
    { text: "Bookmark this page", icon: "🔖" },
    { text: "Summarize this page", icon: "📝" },
    { text: "Search my history", icon: "🔍" },
    { text: "Help me organize tabs", icon: "📂" }
  ];

  if (!isChatSidebarOpen) return null;

  return (
    <div className={`
      fixed right-0 top-0 h-full bg-white border-l border-memori-200 shadow-xl z-50 
      transition-all duration-300 ease-in-out
      ${isMinimized ? 'w-12' : 'w-80'}
    `}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-memori-200 bg-memori-50">
        {!isMinimized && (
          <>
            <div className="flex items-center">
              <Bot className="w-5 h-5 text-memori-500 mr-2" />
              <h3 className="font-semibold text-memori-900">AI Assistant</h3>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsMinimized(true)}
                className="p-1 rounded hover:bg-memori-200 text-memori-600"
                title="Minimize"
              >
                <Minimize2 className="w-4 h-4" />
              </button>
              <button
                onClick={toggleChatSidebar}
                className="p-1 rounded hover:bg-memori-200 text-memori-600"
                title="Close"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </>
        )}
        
        {isMinimized && (
          <button
            onClick={() => setIsMinimized(false)}
            className="w-full p-2 rounded hover:bg-memori-200 text-memori-600"
            title="Expand Chat"
          >
            <Maximize2 className="w-4 h-4 mx-auto" />
          </button>
        )}
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[calc(100vh-200px)]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`
                  max-w-[85%] rounded-lg p-3 
                  ${message.role === 'user' 
                    ? 'bg-memori-500 text-white' 
                    : 'bg-memori-100 text-memori-900'
                  }
                `}>
                  <div className="flex items-start space-x-2">
                    {message.role === 'assistant' && (
                      <Bot className="w-4 h-4 mt-1 flex-shrink-0" />
                    )}
                    {message.role === 'user' && (
                      <User className="w-4 h-4 mt-1 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <div className="whitespace-pre-wrap text-sm">
                        {message.content}
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className={`text-xs ${
                          message.role === 'user' ? 'text-memori-200' : 'text-memori-500'
                        }`}>
                          {formatTimestamp(message.timestamp)}
                        </span>
                        {message.role === 'assistant' && (
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => copyToClipboard(message.content)}
                              className="p-1 rounded hover:bg-memori-200 text-memori-500"
                              title="Copy"
                            >
                              <Copy className="w-3 h-3" />
                            </button>
                            <button
                              className="p-1 rounded hover:bg-memori-200 text-memori-500"
                              title="Good response"
                            >
                              <ThumbsUp className="w-3 h-3" />
                            </button>
                            <button
                              className="p-1 rounded hover:bg-memori-200 text-memori-500"
                              title="Poor response"
                            >
                              <ThumbsDown className="w-3 h-3" />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-memori-100 rounded-lg p-3 max-w-[85%]">
                  <div className="flex items-center space-x-2">
                    <Bot className="w-4 h-4" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-memori-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-memori-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-memori-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Actions */}
          {messages.length <= 1 && (
            <div className="px-4 py-2 border-t border-memori-200">
              <div className="text-xs text-memori-500 mb-2">Quick actions:</div>
              <div className="grid grid-cols-2 gap-1">
                {quickActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => setInputMessage(action.text)}
                    className="text-left p-2 text-xs bg-memori-50 hover:bg-memori-100 rounded border border-memori-200 transition-colors"
                  >
                    <span className="mr-1">{action.icon}</span>
                    {action.text}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t border-memori-200">
            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about your browsing..."
                  className="w-full px-3 py-2 pr-10 border border-memori-200 rounded-lg focus:outline-none focus:border-memori-500 text-sm"
                  disabled={isLoading}
                />
                <button
                  onClick={handleVoiceInput}
                  className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded ${
                    isVoiceSearchActive 
                      ? 'text-red-500 animate-pulse' 
                      : 'text-memori-400 hover:text-memori-600'
                  }`}
                  title="Voice input"
                >
                  <Mic className="w-4 h-4" />
                </button>
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="p-2 bg-memori-500 text-white rounded-lg hover:bg-memori-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Send message"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatSidebar;
