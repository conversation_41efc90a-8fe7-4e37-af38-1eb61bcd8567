use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use sysinfo::{System, Pid};
use tokio::time::interval;
use tracing::{info, warn, error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub tab_id: String,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f32,
    pub load_time_ms: u64,
    pub dom_content_loaded_ms: u64,
    pub js_heap_size_mb: f64,
    pub network_requests: u32,
    pub render_time_ms: u64,
    pub fps: f32,
    pub energy_impact: EnergyImpact,
    pub timestamp: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum EnergyImpact {
    Low,
    Medium,
    High,
    VeryHigh,
}

#[derive(Debug)]
struct TabPerformanceState {
    start_time: Instant,
    last_update: Instant,
    metrics_history: Vec<PerformanceMetrics>,
    memory_baseline: f64,
    cpu_baseline: f32,
    is_active: bool,
    throttled: bool,
}

#[derive(Debug)]
pub struct PerformanceMonitor {
    system: Arc<RwLock<System>>,
    tab_states: Arc<RwLock<HashMap<String, TabPerformanceState>>>,
    global_metrics: Arc<RwLock<GlobalPerformanceMetrics>>,
    monitoring_active: bool,
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self {
            system: Arc::new(RwLock::new(System::new())),
            tab_states: Arc::new(RwLock::new(HashMap::new())),
            global_metrics: Arc::new(RwLock::new(GlobalPerformanceMetrics::default())),
            monitoring_active: false,
        }
    }
}

#[derive(Debug, Default, Clone)]
struct GlobalPerformanceMetrics {
    total_memory_mb: f64,
    total_cpu_percent: f32,
    active_tabs: u32,
    suspended_tabs: u32,
    network_usage_mbps: f64,
    battery_impact: f32,
}

impl PerformanceMonitor {
    pub async fn new() -> Result<Self> {
        let mut system = System::new_all();
        system.refresh_all();

        Ok(Self {
            system: Arc::new(RwLock::new(system)),
            tab_states: Arc::new(RwLock::new(HashMap::new())),
            global_metrics: Arc::new(RwLock::new(GlobalPerformanceMetrics::default())),
            monitoring_active: false,
        })
    }

    pub async fn start_monitoring(&self, tab_id: &str) -> Result<()> {
        let state = TabPerformanceState {
            start_time: Instant::now(),
            last_update: Instant::now(),
            metrics_history: Vec::new(),
            memory_baseline: 0.0,
            cpu_baseline: 0.0,
            is_active: true,
            throttled: false,
        };

        self.tab_states.write().insert(tab_id.to_string(), state);
        
        // Start background monitoring if not already running
        if !self.monitoring_active {
            self.start_background_monitoring().await?;
        }

        info!("Started performance monitoring for tab: {}", tab_id);
        Ok(())
    }

    pub async fn stop_monitoring(&self, tab_id: &str) -> Result<()> {
        if let Some(state) = self.tab_states.write().remove(tab_id) {
            let duration = state.start_time.elapsed();
            info!(
                "Stopped monitoring tab: {} (duration: {:?}, metrics collected: {})",
                tab_id,
                duration,
                state.metrics_history.len()
            );
        }
        Ok(())
    }

    pub async fn update_metrics(&self, tab_id: &str, metrics: PerformanceMetrics) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.metrics_history.push(metrics.clone());
            state.last_update = Instant::now();

            // Keep only last 100 metrics to prevent memory bloat
            if state.metrics_history.len() > 100 {
                state.metrics_history.drain(0..50);
            }

            // Check for performance issues
            self.analyze_performance_issues(tab_id, &metrics).await?;
        }
        Ok(())
    }

    pub async fn get_tab_metrics(&self, tab_id: &str) -> Option<PerformanceMetrics> {
        self.tab_states
            .read()
            .get(tab_id)?
            .metrics_history
            .last()
            .cloned()
    }

    pub async fn get_global_metrics(&self) -> GlobalPerformanceMetrics {
        (*self.global_metrics.read()).clone()
    }

    pub async fn throttle_tab(&self, tab_id: &str, throttle: bool) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.throttled = throttle;
            info!("Tab {} throttling: {}", tab_id, throttle);
        }
        Ok(())
    }

    pub async fn suspend_tab(&self, tab_id: &str) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.is_active = false;
            info!("Suspended tab: {}", tab_id);
        }
        Ok(())
    }

    pub async fn resume_tab(&self, tab_id: &str) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.is_active = true;
            info!("Resumed tab: {}", tab_id);
        }
        Ok(())
    }

    async fn start_background_monitoring(&self) -> Result<()> {
        let system = Arc::clone(&self.system);
        let tab_states = Arc::clone(&self.tab_states);
        let global_metrics = Arc::clone(&self.global_metrics);

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(5));
            
            loop {
                interval.tick().await;
                
                // Update system information
                system.write().refresh_all();
                
                // Calculate global metrics
                let mut total_memory = 0.0;
                let mut total_cpu = 0.0;
                let mut active_tabs = 0;
                let mut suspended_tabs = 0;

                {
                    let states = tab_states.read();
                    for state in states.values() {
                        if let Some(latest_metrics) = state.metrics_history.last() {
                            total_memory += latest_metrics.memory_usage_mb;
                            total_cpu += latest_metrics.cpu_usage_percent;
                            
                            if state.is_active {
                                active_tabs += 1;
                            } else {
                                suspended_tabs += 1;
                            }
                        }
                    }
                }

                // Update global metrics
                {
                    let mut global = global_metrics.write();
                    global.total_memory_mb = total_memory;
                    global.total_cpu_percent = total_cpu;
                    global.active_tabs = active_tabs;
                    global.suspended_tabs = suspended_tabs;
                }

                // Check for system-wide performance issues
                Self::check_system_performance(&system, &global_metrics).await;
            }
        });

        Ok(())
    }

    async fn analyze_performance_issues(&self, tab_id: &str, metrics: &PerformanceMetrics) -> Result<()> {
        // Memory usage warnings
        if metrics.memory_usage_mb > 500.0 {
            warn!("High memory usage in tab {}: {:.1} MB", tab_id, metrics.memory_usage_mb);
            
            if metrics.memory_usage_mb > 1000.0 {
                error!("Critical memory usage in tab {}: {:.1} MB", tab_id, metrics.memory_usage_mb);
                // Consider automatic tab suspension
                self.suspend_tab(tab_id).await?;
            }
        }

        // CPU usage warnings
        if metrics.cpu_usage_percent > 50.0 {
            warn!("High CPU usage in tab {}: {:.1}%", tab_id, metrics.cpu_usage_percent);
            
            if metrics.cpu_usage_percent > 80.0 {
                error!("Critical CPU usage in tab {}: {:.1}%", tab_id, metrics.cpu_usage_percent);
                // Consider throttling
                self.throttle_tab(tab_id, true).await?;
            }
        }

        // Load time warnings
        if metrics.load_time_ms > 5000 {
            warn!("Slow page load in tab {}: {} ms", tab_id, metrics.load_time_ms);
        }

        // Energy impact assessment
        match metrics.energy_impact {
            EnergyImpact::High | EnergyImpact::VeryHigh => {
                warn!("High energy impact in tab {}: {:?}", tab_id, metrics.energy_impact);
            }
            _ => {}
        }

        Ok(())
    }

    async fn check_system_performance(
        system: &Arc<RwLock<System>>,
        global_metrics: &Arc<RwLock<GlobalPerformanceMetrics>>,
    ) {
        let sys = system.read();
        let global = global_metrics.read();

        // Check total system memory
        let total_memory_gb = sys.total_memory() as f64 / 1024.0 / 1024.0 / 1024.0;
        let used_memory_gb = sys.used_memory() as f64 / 1024.0 / 1024.0 / 1024.0;
        let memory_usage_percent = (used_memory_gb / total_memory_gb) * 100.0;

        if memory_usage_percent > 85.0 {
            warn!("High system memory usage: {:.1}%", memory_usage_percent);
        }

        // Check CPU usage
        if global.total_cpu_percent > 70.0 {
            warn!("High total CPU usage from browser: {:.1}%", global.total_cpu_percent);
        }

        // Log performance summary
        info!(
            "Performance summary - Memory: {:.1} MB, CPU: {:.1}%, Active tabs: {}, Suspended: {}",
            global.total_memory_mb,
            global.total_cpu_percent,
            global.active_tabs,
            global.suspended_tabs
        );
    }

    pub fn calculate_energy_impact(metrics: &PerformanceMetrics) -> EnergyImpact {
        let score = (metrics.cpu_usage_percent / 100.0) * 0.4
            + (metrics.memory_usage_mb / 1000.0) as f32 * 0.3
            + (metrics.network_requests as f32 / 100.0) * 0.2
            + (metrics.render_time_ms as f32 / 1000.0) * 0.1;

        match score {
            s if s < 0.2 => EnergyImpact::Low,
            s if s < 0.5 => EnergyImpact::Medium,
            s if s < 0.8 => EnergyImpact::High,
            _ => EnergyImpact::VeryHigh,
        }
    }

    pub async fn get_performance_recommendations(&self, tab_id: &str) -> Vec<String> {
        let mut recommendations = Vec::new();

        if let Some(state) = self.tab_states.read().get(tab_id) {
            if let Some(metrics) = state.metrics_history.last() {
                if metrics.memory_usage_mb > 300.0 {
                    recommendations.push("Consider closing unused tabs to reduce memory usage".to_string());
                }

                if metrics.cpu_usage_percent > 30.0 {
                    recommendations.push("This tab is using high CPU - consider pausing videos or animations".to_string());
                }

                if metrics.load_time_ms > 3000 {
                    recommendations.push("Page loads slowly - check your internet connection".to_string());
                }

                if metrics.network_requests > 50 {
                    recommendations.push("Many network requests detected - consider using an ad blocker".to_string());
                }

                match metrics.energy_impact {
                    EnergyImpact::High | EnergyImpact::VeryHigh => {
                        recommendations.push("This tab has high energy impact - consider suspending when not in use".to_string());
                    }
                    _ => {}
                }
            }
        }

        recommendations
    }
}
