{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2225463790103693989, "path": 15872171666676023849, "deps": [[2828590642173593838, "cfg_if", false, 8324370001129048503], [5170503507811329045, "build_script_build", false, 8217937101443220756]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-8a01b6bd704b487e\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}