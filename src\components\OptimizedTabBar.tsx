import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { X, Plus, Volume2, Search, Bookmark, Zap, AlertTriangle } from 'lucide-react';
import { useBrowserStore } from '../stores/browserStore';

// Virtualization for large tab counts
import { FixedSizeList as List } from 'react-window';

interface TabItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    tabs: any[];
    activeTabId: string | null;
    onTabClick: (tab: any) => void;
    onTabClose: (tabId: string) => void;
    onTabMute: (tabId: string, isPlaying: boolean) => void;
  };
}

const TabItem = React.memo<TabItemProps>(({ index, style, data }) => {
  const { tabs, activeTabId, onTabClick, onTabClose, onTabMute } = data;
  const tab = tabs[index];

  if (!tab) return null;

  const isActive = tab.id === activeTabId;
  const isRecentlyAccessed = Date.now() - tab.last_accessed < 5 * 60 * 1000;

  const handleClick = useCallback(() => {
    onTabClick(tab);
  }, [tab, onTabClick]);

  const handleClose = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onTabClose(tab.id);
  }, [tab.id, onTabClose]);

  const handleMute = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onTabMute(tab.id, tab.is_playing_audio);
  }, [tab.id, tab.is_playing_audio, onTabMute]);

  const getTabIcon = useMemo(() => {
    if (tab.is_loading) {
      return <div className="w-4 h-4 mr-2 animate-spin border-2 border-memori-500 border-t-transparent rounded-full" />;
    }
    
    if (tab.favicon) {
      return <img src={tab.favicon} alt="" className="w-4 h-4 mr-2 flex-shrink-0" loading="lazy" />;
    }
    
    return <div className="w-4 h-4 mr-2 bg-memori-300 rounded flex-shrink-0" />;
  }, [tab.is_loading, tab.favicon]);

  const getTabIndicators = useMemo(() => {
    const indicators = [];
    
    // Audio indicator
    if (tab.is_playing_audio) {
      indicators.push(
        <button
          key="audio"
          onClick={handleMute}
          className="p-1 rounded hover:bg-memori-300 text-memori-500 flex-shrink-0 animate-pulse"
          title="🔊 Audio playing - Click to mute"
          aria-label="Mute tab"
        >
          <Volume2 className="w-3 h-3" />
        </button>
      );
    }
    
    // Bookmark suggestion
    if (tab.bookmark_suggested) {
      indicators.push(
        <div
          key="bookmark"
          className="p-1 text-yellow-600 flex-shrink-0 animate-bounce"
          title="💾 Suggested for bookmarking"
        >
          <Bookmark className="w-3 h-3" />
        </div>
      );
    }
    
    // High visit count
    if (tab.visit_count > 10) {
      indicators.push(
        <div
          key="frequent"
          className="p-1 text-green-600 flex-shrink-0"
          title={`⚡ Frequently visited (${tab.visit_count} times)`}
        >
          <Zap className="w-3 h-3" />
        </div>
      );
    }

    // Performance warning
    if (tab.memory_usage > 500) {
      indicators.push(
        <div
          key="performance"
          className="p-1 text-red-600 flex-shrink-0"
          title="⚠️ High memory usage"
        >
          <AlertTriangle className="w-3 h-3" />
        </div>
      );
    }
    
    return indicators;
  }, [tab.is_playing_audio, tab.bookmark_suggested, tab.visit_count, tab.memory_usage, handleMute]);

  const formatLastAccessed = useMemo(() => {
    const now = Date.now();
    const diff = now - tab.last_accessed;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${Math.floor(hours / 24)}d`;
  }, [tab.last_accessed]);

  return (
    <div style={style}>
      <div
        className={`
          flex items-center max-w-xs min-w-0 px-3 py-2 mx-1 rounded-t-lg cursor-pointer
          transition-all duration-200 group relative
          ${isActive 
            ? 'bg-white border-t-2 border-memori-500 text-memori-900 shadow-sm' 
            : 'bg-memori-100 hover:bg-memori-200 text-memori-700'
          }
        `}
        onClick={handleClick}
        title={`${tab.title}\n${tab.url}\nLast accessed: ${formatLastAccessed}\nVisits: ${tab.visit_count}\nMemory: ${tab.memory_usage || 0}MB`}
        role="tab"
        aria-selected={isActive}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        {getTabIcon}
        
        <span className="truncate text-sm font-medium flex-1">
          {tab.title}
        </span>
        
        {/* Tab indicators */}
        <div className="flex items-center ml-2">
          {getTabIndicators}
          
          {/* Close button */}
          <button
            onClick={handleClose}
            className="ml-1 p-1 rounded hover:bg-memori-300 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Close tab"
          >
            <X className="w-3 h-3" />
          </button>
        </div>

        {/* Recent access indicator */}
        {!isActive && isRecentlyAccessed && (
          <div 
            className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" 
            title="Recently accessed"
            aria-label="Recently accessed"
          />
        )}
      </div>
    </div>
  );
});

TabItem.displayName = 'TabItem';

const OptimizedTabBar: React.FC = () => {
  const { 
    tabs, 
    activeTabId, 
    createTab, 
    closeTab, 
    switchTab, 
    searchTabs,
    getAudioTabs,
    suggestBookmark,
    setTabAudioState
  } = useBrowserStore();
  
  const [showTabSearch, setShowTabSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTabs, setFilteredTabs] = useState(tabs);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<List>(null);

  // Debounced search to improve performance
  const debouncedSearch = useCallback(
    useMemo(() => {
      let timeoutId: NodeJS.Timeout;
      return (query: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          if (query.trim()) {
            const results = searchTabs(query);
            setFilteredTabs(results);
          } else {
            setFilteredTabs(tabs);
          }
        }, 150);
      };
    }, [searchTabs, tabs]),
    [searchTabs, tabs]
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // Memoized handlers to prevent unnecessary re-renders
  const handleTabClick = useCallback((tab: any) => {
    switchTab(tab.id);
    
    // Auto-suggest bookmark for frequently visited tabs
    if (tab.visit_count > 5 && !tab.bookmark_suggested) {
      setTimeout(() => suggestBookmark(tab.id), 2000);
    }
  }, [switchTab, suggestBookmark]);

  const handleTabClose = useCallback((tabId: string) => {
    closeTab(tabId);
  }, [closeTab]);

  const handleTabMute = useCallback((tabId: string, isPlaying: boolean) => {
    setTabAudioState(tabId, !isPlaying);
  }, [setTabAudioState]);

  const handleCreateTab = useCallback(() => {
    createTab();
  }, [createTab]);

  const handleSearchToggle = useCallback(() => {
    setShowTabSearch(prev => {
      const newValue = !prev;
      if (newValue) {
        // Focus search input when opening
        setTimeout(() => searchInputRef.current?.focus(), 100);
      } else {
        setSearchQuery('');
      }
      return newValue;
    });
  }, []);

  const handleSearchClose = useCallback(() => {
    setShowTabSearch(false);
    setSearchQuery('');
  }, []);

  // Memoized data for virtualized list
  const listData = useMemo(() => ({
    tabs: filteredTabs,
    activeTabId,
    onTabClick: handleTabClick,
    onTabClose: handleTabClose,
    onTabMute: handleTabMute,
  }), [filteredTabs, activeTabId, handleTabClick, handleTabClose, handleTabMute]);

  // Audio tabs count
  const audioTabsCount = useMemo(() => getAudioTabs().length, [getAudioTabs]);

  // Performance metrics
  const performanceMetrics = useMemo(() => {
    const totalMemory = tabs.reduce((sum, tab) => sum + (tab.memory_usage || 0), 0);
    const highMemoryTabs = tabs.filter(tab => (tab.memory_usage || 0) > 300).length;
    
    return { totalMemory, highMemoryTabs };
  }, [tabs]);

  return (
    <div className="flex flex-col bg-memori-50 border-b border-memori-200">
      {/* Tab Search Bar */}
      {showTabSearch && (
        <div className="flex items-center px-4 py-2 bg-white border-b border-memori-200">
          <Search className="w-4 h-4 text-memori-400 mr-2" />
          <input
            ref={searchInputRef}
            type="text"
            placeholder="🔍 Search tabs by title, URL, or domain..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1 outline-none text-sm"
            aria-label="Search tabs"
          />
          <button
            onClick={handleSearchClose}
            className="ml-2 p-1 rounded hover:bg-memori-100"
            aria-label="Close search"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
      
      {/* Tab Bar */}
      <div className="flex items-center px-2 py-1">
        {/* Tab Search Toggle */}
        <button
          onClick={handleSearchToggle}
          className={`
            flex items-center justify-center w-8 h-8 mr-2 rounded-full 
            ${showTabSearch ? 'bg-memori-500 text-white' : 'hover:bg-memori-200 text-memori-600'}
          `}
          title="🔍 Search tabs (Ctrl+Shift+A)"
          aria-label="Toggle tab search"
        >
          <Search className="w-4 h-4" />
        </button>

        {/* Virtualized Tab List for performance with many tabs */}
        <div className="flex-1 overflow-hidden">
          {filteredTabs.length > 20 ? (
            <List
              ref={listRef}
              height={40}
              itemCount={filteredTabs.length}
              itemSize={200}
              itemData={listData}
              layout="horizontal"
              className="flex"
            >
              {TabItem}
            </List>
          ) : (
            <div className="flex overflow-x-auto">
              {filteredTabs.map((tab, index) => (
                <TabItem
                  key={tab.id}
                  index={index}
                  style={{ display: 'inline-block' }}
                  data={listData}
                />
              ))}
            </div>
          )}
        </div>
        
        {/* New Tab Button */}
        <button
          onClick={handleCreateTab}
          className="flex items-center justify-center w-8 h-8 ml-2 rounded-full hover:bg-memori-200 text-memori-600 flex-shrink-0"
          title="➕ New tab (Ctrl+T)"
          aria-label="Create new tab"
        >
          <Plus className="w-4 h-4" />
        </button>

        {/* Status Indicators */}
        <div className="flex items-center ml-4 space-x-2">
          {/* Audio tabs indicator */}
          {audioTabsCount > 0 && (
            <div className="flex items-center px-2 py-1 bg-memori-500 text-white rounded-full text-xs animate-pulse">
              <Volume2 className="w-3 h-3 mr-1" />
              {audioTabsCount}
            </div>
          )}

          {/* Performance indicator */}
          {performanceMetrics.highMemoryTabs > 0 && (
            <div 
              className="flex items-center px-2 py-1 bg-yellow-500 text-white rounded-full text-xs"
              title={`${performanceMetrics.highMemoryTabs} tabs using high memory (${performanceMetrics.totalMemory}MB total)`}
            >
              <AlertTriangle className="w-3 h-3 mr-1" />
              {performanceMetrics.highMemoryTabs}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(OptimizedTabBar);
