{"rustc": 1842507548689473721, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 9623890445795218302, "deps": [[966925859616469517, "ahash", false, 9509266967900709254], [9150530836556604396, "allocator_api2", false, 163220047463368912]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-472adb9d1dd297b5\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}