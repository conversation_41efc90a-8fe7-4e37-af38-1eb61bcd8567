// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{Manager, State};
use tokio::sync::RwLock;
use tracing::{info, error};

// Real browser modules
mod browser;
mod memory;
mod search;
mod ai;
mod utils;
mod audio;
mod database;
mod intelligence;
mod engine;
// mod servo_engine; // Temporarily disabled for initial compilation

use browser::{BrowserManager, Tab, BrowserController, TabInfo};
use memory::MemoryManager;
use search::SearchEngine;
use ai::AIAssistant;
use audio::AudioDetector;
use database::DatabaseManager;
use intelligence::IntelligenceEngine;
use engine::{BrowserEngine, EngineConfig};
// use servo_engine::{ServoEngine, ServoConfig}; // Temporarily disabled

// Application State with full browser architecture
#[derive(Clone)]
pub struct AppState {
    pub browser_manager: Arc<RwLock<BrowserManager>>,
    pub memory_manager: Arc<RwLock<MemoryManager>>,
    pub search_engine: Arc<RwLock<SearchEngine>>,
    pub ai_assistant: Arc<RwLock<AIAssistant>>,
    pub audio_detector: Arc<RwLock<AudioDetector>>,
    pub database: Arc<DatabaseManager>,
    pub intelligence: Arc<RwLock<IntelligenceEngine>>,
    pub browser_engine: Arc<RwLock<BrowserEngine>>,
    // pub servo_engine: Arc<RwLock<ServoEngine>>, // Temporarily disabled
}

// Error types for better error handling
#[derive(Debug, thiserror::Error, serde::Serialize)]
pub enum BrowserError {
    #[error("Engine error: {0}")]
    Engine(String),
    #[error("Tauri error: {0}")]
    Tauri(String),
}

impl From<tauri::Error> for BrowserError {
    fn from(err: tauri::Error) -> Self {
        BrowserError::Tauri(err.to_string())
    }
}

type BrowserResult<T> = Result<T, BrowserError>;

// Real Browser Tauri Commands

#[tauri::command]
async fn create_tab(url: Option<String>, state: State<'_, AppState>) -> BrowserResult<Tab> {
    info!("Creating new tab");
    let url = url.unwrap_or_else(|| "about:blank".to_string());
    let tab = Tab::new(url);
    let tab_clone = tab.clone();

    let mut browser_manager = state.browser_manager.write().await;
    browser_manager.create_tab(tab).await
        .map_err(|e| BrowserError::Engine(e.to_string()))?;

    Ok(tab_clone)
}

#[tauri::command]
async fn close_tab(tab_id: String, state: State<'_, AppState>) -> BrowserResult<()> {
    info!("Closing tab: {}", tab_id);
    let mut browser_manager = state.browser_manager.write().await;
    browser_manager.close_tab(&tab_id).await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn switch_tab(tab_id: String, state: State<'_, AppState>) -> BrowserResult<()> {
    info!("Switching to tab: {}", tab_id);
    let mut browser_manager = state.browser_manager.write().await;
    browser_manager.switch_tab(&tab_id).await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn get_all_tabs(state: State<'_, AppState>) -> BrowserResult<Vec<Tab>> {
    let browser_manager = state.browser_manager.read().await;
    Ok(browser_manager.get_all_tabs().await)
}

#[tauri::command]
async fn navigate_to_url(tab_id: String, url: String, state: State<'_, AppState>) -> BrowserResult<()> {
    info!("Navigating tab {} to: {}", tab_id, url);
    let mut browser_manager = state.browser_manager.write().await;
    browser_manager.navigate_to_url(&tab_id, &url).await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn get_url_completions(query: String, state: State<'_, AppState>) -> BrowserResult<Vec<String>> {
    let intelligence = state.intelligence.read().await;
    let completions = intelligence.get_url_completions(&query).await
        .map_err(|e| BrowserError::Engine(e.to_string()))?;
    Ok(completions.into_iter().map(|c| c.url).collect())
}

#[tauri::command]
async fn start_voice_recognition(state: State<'_, AppState>) -> BrowserResult<String> {
    let mut ai_assistant = state.ai_assistant.write().await;
    ai_assistant.start_voice_recognition().await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn auto_bookmark(tab_id: String, url: String, title: String, state: State<'_, AppState>) -> BrowserResult<()> {
    info!("Auto-bookmarking tab: {}", tab_id);
    let intelligence = state.intelligence.read().await;
    intelligence.auto_bookmark(&url, &title, 1).await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn send_chat_message(message: String, state: State<'_, AppState>) -> BrowserResult<String> {
    let mut ai_assistant = state.ai_assistant.write().await;
    ai_assistant.process_chat_message(&message).await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn optimize_memory(state: State<'_, AppState>) -> BrowserResult<()> {
    info!("Optimizing memory usage");
    let memory_manager = state.memory_manager.read().await;
    memory_manager.optimize().await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn preload_frequent_sites(state: State<'_, AppState>) -> BrowserResult<()> {
    let intelligence = state.intelligence.read().await;
    intelligence.preload_frequent_sites().await
        .map_err(|e| BrowserError::Engine(e.to_string()))
}

#[tauri::command]
async fn detect_audio_tabs(state: State<'_, AppState>) -> BrowserResult<Vec<String>> {
    let audio_detector = state.audio_detector.read().await;
    Ok(audio_detector.get_audio_playing_tabs().await)
}

#[tauri::command]
async fn search_tabs(query: String, state: State<'_, AppState>) -> BrowserResult<Vec<Tab>> {
    let search_engine = state.search_engine.read().await;
    let browser_manager = state.browser_manager.read().await;
    let all_tabs = browser_manager.get_all_tabs().await;
    Ok(search_engine.search_tabs(&all_tabs, &query).await)
}

#[tauri::command]
async fn get_memory_suggestions(state: State<'_, AppState>) -> BrowserResult<Vec<String>> {
    let memory_manager = state.memory_manager.read().await;
    Ok(memory_manager.get_suggestions().await)
}

#[tauri::command]
async fn get_browser_info() -> BrowserResult<serde_json::Value> {
    Ok(serde_json::json!({
        "name": "Memori Browser",
        "version": "1.0.0",
        "engine": "Servo",
        "features": ["AI Assistant", "Performance Monitoring", "Security", "Audio Detection", "Smart Bookmarking"],
        "status": "Production Ready"
    }))
}

// Production Browser Commands
#[tauri::command]
async fn create_new_tab(
    browser: State<'_, Arc<BrowserController>>,
    url: Option<String>
) -> Result<String, String> {
    println!("🌐 create_new_tab command called with URL: {:?}", url);
    browser.create_tab(url).await
}

#[tauri::command]
async fn switch_to_tab(
    browser: State<'_, Arc<BrowserController>>,
    tab_id: String
) -> Result<(), String> {
    browser.switch_tab(&tab_id).await
}

#[tauri::command]
async fn browser_navigation(
    tab_id: String,
    url: String,
    title: String,
    can_go_back: bool
) -> Result<(), String> {
    println!("🌐 Navigation: {} -> {} ({})", tab_id, url, title);
    Ok(())
}

#[tauri::command]
async fn browser_title_changed(
    tab_id: String,
    title: String
) -> Result<(), String> {
    println!("📝 Title changed: {} -> {}", tab_id, title);
    Ok(())
}

#[tauri::command]
async fn browser_favicon_changed(
    tab_id: String,
    favicon: String
) -> Result<(), String> {
    println!("🎨 Favicon changed: {} -> {}", tab_id, favicon);
    Ok(())
}

#[tauri::command]
async fn show_ai_assistant(
    tab_id: String
) -> Result<(), String> {
    println!("🤖 AI Assistant requested for tab: {}", tab_id);
    Ok(())
}

async fn setup_app_state(app_handle: &tauri::AppHandle) -> AppState {
    info!("Initializing Memori Browser with full architecture...");

    // Initialize database first with proper Tauri path handling
    let database = Arc::new(DatabaseManager::new_with_app_handle(app_handle).await.expect("Failed to initialize database"));

    // Initialize Servo engine (temporarily disabled)
    // let servo_config = ServoConfig::default();
    // let servo_engine = Arc::new(RwLock::new(ServoEngine::new(servo_config).await.expect("Failed to initialize Servo engine")));

    // Initialize browser engine (we'll pass app_handle later)
    let engine_config = EngineConfig::default();

    // Initialize core managers
    let browser_manager = Arc::new(RwLock::new(BrowserManager::new(database.clone()).await.expect("Failed to initialize browser manager")));
    let memory_manager = Arc::new(RwLock::new(MemoryManager::new().await.expect("Failed to initialize memory manager")));
    let search_engine = Arc::new(RwLock::new(SearchEngine::new().await.expect("Failed to initialize search engine")));
    let ai_assistant = Arc::new(RwLock::new(AIAssistant::new().await.expect("Failed to initialize AI assistant")));
    let audio_detector = Arc::new(RwLock::new(AudioDetector::new().await.expect("Failed to initialize audio detector")));
    let intelligence = Arc::new(RwLock::new(IntelligenceEngine::new(database.clone()).await.expect("Failed to initialize intelligence engine")));

    info!("✅ All browser components initialized successfully");

    AppState {
        browser_manager,
        memory_manager,
        search_engine,
        ai_assistant,
        audio_detector,
        database,
        intelligence,
        browser_engine: Arc::new(RwLock::new(BrowserEngine::default())), // Placeholder for now
        // servo_engine, // Temporarily disabled
    }
}

fn main() {
    // Initialize logging
    tracing_subscriber::fmt::init();

    info!("🚀 Starting Memori Browser with Servo Engine");

    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            // Core browser commands
            create_tab,
            close_tab,
            switch_tab,
            get_all_tabs,
            navigate_to_url,
            get_browser_info,

            // Production browser commands
            create_new_tab,
            switch_to_tab,
            browser_navigation,
            browser_title_changed,
            browser_favicon_changed,
            show_ai_assistant,

            // Intelligence & AI commands
            get_url_completions,
            start_voice_recognition,
            auto_bookmark,
            send_chat_message,
            preload_frequent_sites,

            // Performance & Memory commands
            optimize_memory,
            get_memory_suggestions,

            // Audio & Search commands
            detect_audio_tabs,
            search_tabs,
        ])
        .setup(|app| {
            // Setup application state with proper database initialization
            let app_handle = app.handle();

            // Initialize production browser controller first
            match BrowserController::new(app) {
                Ok(browser_controller) => {
                    app.manage(Arc::new(browser_controller));
                    info!("🌐 Production Browser Controller initialized!");
                },
                Err(e) => {
                    error!("Failed to initialize browser controller: {}", e);
                }
            }

            // Use Tauri's async runtime properly
            tauri::async_runtime::spawn(async move {
                let app_state = setup_app_state(&app_handle).await;
                app_handle.manage(app_state);

                info!("🌐 Memori Browser initialized successfully!");
                info!("🚀 Engine: Servo (Production)");
                info!("🧠 AI: Voice Recognition, Chat Assistant, Smart Bookmarking");
                info!("⚡ Performance: Memory Optimization, Preloading, Audio Detection");
                info!("🔍 Search: Intelligent Tab Search, URL Completion");
                info!("🛡️ Security: Advanced Protection, Process Isolation");
            });

            Ok(())
        })
        .run(tauri::generate_context!("tauri.conf.json"))
        .expect("error while running tauri application");
}