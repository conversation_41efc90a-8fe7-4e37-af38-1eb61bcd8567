use tauri::{<PERSON>, <PERSON>, <PERSON>B<PERSON>er, WindowUrl, <PERSON>pp<PERSON><PERSON>le};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TabInfo {
    pub id: String,
    pub url: String,
    pub title: String,
    pub favicon: Option<String>,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
    pub is_active: bool,
}

/// Production-quality browser controller
/// This is how real browsers work - multiple webviews in coordinated windows
pub struct BrowserController {
    main_window: Window,
    webview_windows: Arc<Mutex<HashMap<String, Window>>>,
    tabs: Arc<Mutex<HashMap<String, TabInfo>>>,
    active_tab: Arc<Mutex<Option<String>>>,
}

impl BrowserController {
    /// Initialize the browser with proper webview handling
    pub fn new(app: &tauri::App) -> Result<Self, Box<dyn std::error::Error>> {
        // Get the main window - it should be the first window created
        let main_window = app.get_window("main")
            .or_else(|| {
                // If "main" doesn't exist, get any window
                app.windows().values().next().cloned()
            })
            .ok_or("No main window found")?;

        println!("🌐 Browser controller initialized with window: {}", main_window.label());

        Ok(Self {
            main_window,
            webview_windows: Arc::new(Mutex::new(HashMap::new())),
            tabs: Arc::new(Mutex::new(HashMap::new())),
            active_tab: Arc::new(Mutex::new(None)),
        })
    }

    /// Create a new tab with its own webview - THIS IS THE KEY SOLUTION
    pub async fn create_tab(&self, url: Option<String>) -> Result<String, String> {
        let tab_id = Uuid::new_v4().to_string();
        let url = url.unwrap_or_else(|| "https://www.google.com".to_string());

        println!("🚀 Creating new tab {} for URL: {}", tab_id, url);

        // Create webview window positioned within main window
        self.create_webview_window(&tab_id, &url).await?;
        
        // Create tab info
        let tab_info = TabInfo {
            id: tab_id.clone(),
            url: url.clone(),
            title: "Loading...".to_string(),
            favicon: None,
            is_loading: true,
            can_go_back: false,
            can_go_forward: false,
            is_active: true,
        };

        // Store tab info
        {
            let mut tabs = self.tabs.lock().unwrap();
            tabs.insert(tab_id.clone(), tab_info);
        }

        // Set as active tab
        {
            let mut active = self.active_tab.lock().unwrap();
            *active = Some(tab_id.clone());
        }

        Ok(tab_id)
    }

    /// Create a webview window positioned inside main window
    /// This is the magic that makes it work like Chrome
    async fn create_webview_window(&self, tab_id: &str, url: &str) -> Result<(), String> {
        // Get main window position and size
        let main_pos = self.main_window.outer_position()
            .map_err(|e| e.to_string())?;
        let main_size = self.main_window.inner_size()
            .map_err(|e| e.to_string())?;

        // Create webview window positioned inside main window
        // This is positioned to look like it's part of the main window
        let webview_window = WindowBuilder::new(
            &self.main_window.app_handle(),
            tab_id,
            WindowUrl::External(url.parse().map_err(|e| format!("Invalid URL: {}", e))?)
        )
        .title("")
        .decorations(false)
        .always_on_top(false)
        .skip_taskbar(true)
        .resizable(false)
        .position(main_pos.x as f64 + 10.0, main_pos.y as f64 + 120.0) // Below browser chrome
        .inner_size(main_size.width as f64 - 20.0, main_size.height as f64 - 140.0)
        .build()
        .map_err(|e| e.to_string())?;

        // Inject browser enhancements
        let injection_script = self.get_browser_injection_script(tab_id);
        webview_window.eval(&injection_script)
            .map_err(|e| e.to_string())?;

        // Store webview window
        {
            let mut windows = self.webview_windows.lock().unwrap();
            windows.insert(tab_id.to_string(), webview_window);
        }

        Ok(())
    }

    /// Switch to a different tab
    pub async fn switch_tab(&self, tab_id: &str) -> Result<(), String> {
        // Hide current active tab
        if let Some(current_tab) = self.active_tab.lock().unwrap().as_ref() {
            if let Some(window) = self.webview_windows.lock().unwrap().get(current_tab) {
                window.hide().map_err(|e| e.to_string())?;
            }
        }

        // Show new tab
        if let Some(window) = self.webview_windows.lock().unwrap().get(tab_id) {
            window.show().map_err(|e| e.to_string())?;
            window.set_focus().map_err(|e| e.to_string())?;
        }

        // Update active tab
        {
            let mut active = self.active_tab.lock().unwrap();
            *active = Some(tab_id.to_string());
        }

        Ok(())
    }

    /// Close a tab
    pub async fn close_tab(&self, tab_id: &str) -> Result<(), String> {
        // Close webview window
        if let Some(window) = self.webview_windows.lock().unwrap().remove(tab_id) {
            window.close().map_err(|e| e.to_string())?;
        }

        // Remove tab info
        {
            let mut tabs = self.tabs.lock().unwrap();
            tabs.remove(tab_id);
        }

        // If this was the active tab, switch to another
        {
            let mut active = self.active_tab.lock().unwrap();
            if active.as_ref() == Some(&tab_id.to_string()) {
                *active = None;
                // TODO: Switch to another tab if available
            }
        }

        Ok(())
    }

    /// Navigate current tab to URL
    pub async fn navigate_to(&self, url: &str) -> Result<(), String> {
        if let Some(active_tab_id) = self.active_tab.lock().unwrap().as_ref() {
            if let Some(window) = self.webview_windows.lock().unwrap().get(active_tab_id) {
                let script = format!(r#"window.location.href = "{}";"#, url);
                window.eval(&script).map_err(|e| e.to_string())?;
            }
        }
        Ok(())
    }

    /// Get all tabs
    pub fn get_all_tabs(&self) -> Vec<TabInfo> {
        self.tabs.lock().unwrap().values().cloned().collect()
    }

    /// Browser injection script for enhanced functionality
    fn get_browser_injection_script(&self, tab_id: &str) -> String {
        format!(r#"
        (function() {{
            const TAB_ID = '{}';
            
            // Create Memori Browser API
            window.__memoriBrowser = {{
                tabId: TAB_ID,
                
                init: function() {{
                    // Monitor navigation
                    this.setupNavigationMonitoring();
                    // Monitor page changes
                    this.setupPageMonitoring();
                    // Add visual indicator
                    this.addMemoriIndicator();
                }},
                
                setupNavigationMonitoring: function() {{
                    // Override history API
                    const originalPushState = history.pushState;
                    const originalReplaceState = history.replaceState;
                    
                    history.pushState = function() {{
                        originalPushState.apply(history, arguments);
                        window.__memoriBrowser.notifyNavigation();
                    }};
                    
                    history.replaceState = function() {{
                        originalReplaceState.apply(history, arguments);
                        window.__memoriBrowser.notifyNavigation();
                    }};
                    
                    window.addEventListener('popstate', () => {{
                        window.__memoriBrowser.notifyNavigation();
                    }});
                }},
                
                setupPageMonitoring: function() {{
                    // Monitor title changes
                    new MutationObserver(() => {{
                        if (window.__TAURI__) {{
                            window.__TAURI__.invoke('browser_title_changed', {{
                                tabId: TAB_ID,
                                title: document.title
                            }});
                        }}
                    }}).observe(
                        document.querySelector('title') || document.head,
                        {{ subtree: true, characterData: true, childList: true }}
                    );
                }},
                
                notifyNavigation: function() {{
                    if (window.__TAURI__) {{
                        window.__TAURI__.invoke('browser_navigation', {{
                            tabId: TAB_ID,
                            url: window.location.href,
                            title: document.title,
                            canGoBack: window.history.length > 1
                        }});
                    }}
                }},
                
                addMemoriIndicator: function() {{
                    const indicator = document.createElement('div');
                    indicator.style.cssText = `
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 8px 12px;
                        border-radius: 20px;
                        font-size: 12px;
                        z-index: 999999;
                        cursor: pointer;
                        opacity: 0.9;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                    `;
                    indicator.textContent = '🧠 Memori Enhanced';
                    indicator.onclick = () => {{
                        if (window.__TAURI__) {{
                            window.__TAURI__.invoke('show_ai_assistant', {{ tabId: TAB_ID }});
                        }}
                    }};
                    document.body.appendChild(indicator);
                }}
            }};
            
            // Initialize when DOM is ready
            if (document.readyState === 'loading') {{
                document.addEventListener('DOMContentLoaded', () => window.__memoriBrowser.init());
            }} else {{
                window.__memoriBrowser.init();
            }}
            
            console.log('[Memori Browser] Enhanced browsing activated for tab:', TAB_ID);
        }})();
        "#, tab_id)
    }
}
