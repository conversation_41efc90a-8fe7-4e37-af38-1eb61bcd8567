use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

use anyhow::Result;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error, debug};

// Servo Embedder Traits
use servo::embedder_traits::{
    EmbedderProxy, EventLoopWaker, MediaSessionEvent, PermissionPrompt, PermissionRequest,
    PromptDefinition, PromptOrigin, PromptResult,
};
use servo::servo_url::ServoUrl;
use servo::webrender_api::units::DeviceIntSize;
use servo::{BrowserId, TopLevelBrowsingContextId};

// WebRender Integration
use webrender::{RenderApi, Renderer, RendererOptions, Transaction};
use webrender_api::{DocumentId, PipelineId, RenderApiSender, units::*};
use surfman::{Context, ContextAttributeFlags, ContextAttributes, Device, GLVersion};

// Windowing
use winit::{
    event::{Event, WindowEvent},
    event_loop::{ControlFlow, EventLoop, EventLoopProxy},
    window::{Window, WindowBuilder},
    dpi::{LogicalSize, PhysicalSize},
};

use super::{ServoConfig, ServoTab, ServoPerformanceMetrics};

#[derive(Debug, Clone)]
pub enum MemoriEvent {
    TabCreated(String),
    TabClosed(String),
    NavigationStarted(String, ServoUrl),
    NavigationCompleted(String, ServoUrl),
    TitleChanged(String, String),
    LoadingStateChanged(String, bool),
    SecurityStateChanged(String, super::SecurityState),
    PerformanceUpdate(String, ServoPerformanceMetrics),
    AudioStateChanged(String, bool),
    PermissionRequest(String, PermissionRequest),
}

pub struct MemoriEmbedder {
    config: ServoConfig,
    event_sender: Option<EventLoopProxy<MemoriEvent>>,
    windows: Arc<RwLock<HashMap<BrowserId, Arc<Window>>>>,
    render_contexts: Arc<RwLock<HashMap<BrowserId, RenderContext>>>,
    performance_data: Arc<RwLock<HashMap<BrowserId, PerformanceData>>>,
    permission_manager: Arc<PermissionManager>,
}

struct RenderContext {
    device: Device,
    context: Context,
    renderer: Renderer,
    render_api: RenderApiSender,
    document_id: DocumentId,
    pipeline_id: PipelineId,
}

#[derive(Debug, Default)]
struct PerformanceData {
    layout_start: Option<Instant>,
    paint_start: Option<Instant>,
    script_start: Option<Instant>,
    frame_count: u64,
    last_frame_time: Option<Instant>,
}

struct PermissionManager {
    granted_permissions: Arc<RwLock<HashMap<ServoUrl, Vec<PermissionRequest>>>>,
}

impl MemoriEmbedder {
    pub async fn new(config: ServoConfig) -> Result<Self> {
        let permission_manager = Arc::new(PermissionManager {
            granted_permissions: Arc::new(RwLock::new(HashMap::new())),
        });

        Ok(Self {
            config,
            event_sender: None,
            windows: Arc::new(RwLock::new(HashMap::new())),
            render_contexts: Arc::new(RwLock::new(HashMap::new())),
            performance_data: Arc::new(RwLock::new(HashMap::new())),
            permission_manager,
        })
    }

    pub fn set_event_sender(&mut self, sender: EventLoopProxy<MemoriEvent>) {
        self.event_sender = Some(sender);
    }

    fn create_window(&self, browser_id: BrowserId, size: DeviceIntSize) -> Result<Arc<Window>> {
        let event_loop = EventLoop::new();
        let window = WindowBuilder::new()
            .with_title("Memori Browser")
            .with_inner_size(LogicalSize::new(size.width as f64, size.height as f64))
            .with_visible(false) // Start hidden for faster loading
            .build(&event_loop)?;

        let window = Arc::new(window);
        self.windows.write().insert(browser_id, Arc::clone(&window));

        info!("Created window for browser: {:?}", browser_id);
        Ok(window)
    }

    fn create_render_context(&self, browser_id: BrowserId, window: &Window) -> Result<RenderContext> {
        // Create surfman device and context
        let device = Device::create_hardware_device()?;
        let context_attributes = ContextAttributes {
            version: GLVersion::new(3, 3),
            flags: ContextAttributeFlags::ALPHA | ContextAttributeFlags::DEPTH,
        };
        let context = device.create_context(&context_attributes)?;

        // Create WebRender renderer
        let opts = RendererOptions {
            device_pixel_ratio: window.scale_factor() as f32,
            clear_color: Some([1.0, 1.0, 1.0, 1.0].into()),
            enable_aa: true,
            enable_subpixel_aa: true,
            ..Default::default()
        };

        let size = window.inner_size();
        let framebuffer_size = DeviceIntSize::new(size.width as i32, size.height as i32);

        let (renderer, render_api) = Renderer::new(
            device.clone(),
            Box::new(context.clone()),
            opts,
            None,
        )?;

        let document_id = render_api.add_document(framebuffer_size);
        let pipeline_id = PipelineId::new();

        let render_context = RenderContext {
            device,
            context,
            renderer,
            render_api,
            document_id,
            pipeline_id,
        };

        self.render_contexts.write().insert(browser_id, render_context);
        info!("Created render context for browser: {:?}", browser_id);

        Ok(self.render_contexts.read().get(&browser_id).unwrap().clone())
    }

    fn send_event(&self, event: MemoriEvent) {
        if let Some(ref sender) = self.event_sender {
            if let Err(e) = sender.send_event(event) {
                error!("Failed to send event: {:?}", e);
            }
        }
    }

    fn update_performance_metrics(&self, browser_id: BrowserId, tab_id: &str) {
        if let Some(perf_data) = self.performance_data.read().get(&browser_id) {
            let now = Instant::now();
            
            let layout_time = perf_data.layout_start
                .map(|start| now.duration_since(start).as_millis() as f64)
                .unwrap_or(0.0);

            let paint_time = perf_data.paint_start
                .map(|start| now.duration_since(start).as_millis() as f64)
                .unwrap_or(0.0);

            let frame_rate = if let Some(last_frame) = perf_data.last_frame_time {
                1000.0 / now.duration_since(last_frame).as_millis() as f32
            } else {
                60.0
            };

            let metrics = ServoPerformanceMetrics {
                layout_time_ms: layout_time,
                paint_time_ms: paint_time,
                script_time_ms: 0.0, // TODO: Implement script timing
                composite_time_ms: 0.0, // TODO: Implement composite timing
                memory_usage_mb: 0.0, // TODO: Get from Servo
                frame_rate,
                energy_impact: super::EnergyImpact::Low, // TODO: Calculate based on metrics
            };

            self.send_event(MemoriEvent::PerformanceUpdate(tab_id.to_string(), metrics));
        }
    }
}

impl EmbedderProxy for MemoriEmbedder {
    fn create_event_loop_waker(&mut self) -> Box<dyn EventLoopWaker> {
        Box::new(MemoriEventLoopWaker::new())
    }

    fn on_load_start(&self, browser_id: BrowserId, top_level_browsing_context_id: TopLevelBrowsingContextId) {
        debug!("Load started for browser: {:?}", browser_id);
        
        // Initialize performance tracking
        self.performance_data.write().insert(browser_id, PerformanceData::default());
        
        // Send loading event
        let tab_id = format!("{:?}", browser_id); // TODO: Map browser_id to tab_id
        self.send_event(MemoriEvent::LoadingStateChanged(tab_id, true));
    }

    fn on_load_complete(&self, browser_id: BrowserId, top_level_browsing_context_id: TopLevelBrowsingContextId) {
        debug!("Load completed for browser: {:?}", browser_id);
        
        let tab_id = format!("{:?}", browser_id); // TODO: Map browser_id to tab_id
        self.send_event(MemoriEvent::LoadingStateChanged(tab_id.clone(), false));
        
        // Update performance metrics
        self.update_performance_metrics(browser_id, &tab_id);
    }

    fn on_title_change(&self, browser_id: BrowserId, title: Option<String>) {
        let title = title.unwrap_or_else(|| "Untitled".to_string());
        debug!("Title changed for browser {:?}: {}", browser_id, title);
        
        let tab_id = format!("{:?}", browser_id); // TODO: Map browser_id to tab_id
        self.send_event(MemoriEvent::TitleChanged(tab_id, title));
    }

    fn on_url_change(&self, browser_id: BrowserId, url: ServoUrl) {
        debug!("URL changed for browser {:?}: {}", browser_id, url);
        
        let tab_id = format!("{:?}", browser_id); // TODO: Map browser_id to tab_id
        self.send_event(MemoriEvent::NavigationCompleted(tab_id, url));
    }

    fn on_history_change(&self, browser_id: BrowserId, can_go_back: bool, can_go_forward: bool) {
        debug!("History changed for browser {:?}: back={}, forward={}", browser_id, can_go_back, can_go_forward);
        // TODO: Update tab navigation state
    }

    fn on_animating_change(&self, browser_id: BrowserId, animating: bool) {
        debug!("Animation state changed for browser {:?}: {}", browser_id, animating);
        // TODO: Update performance metrics based on animation state
    }

    fn on_shutdown_complete(&self, browser_id: BrowserId) {
        info!("Shutdown complete for browser: {:?}", browser_id);
        
        // Cleanup resources
        self.windows.write().remove(&browser_id);
        self.render_contexts.write().remove(&browser_id);
        self.performance_data.write().remove(&browser_id);
    }

    fn on_panic(&self, browser_id: BrowserId, reason: String, backtrace: Option<String>) {
        error!("Panic in browser {:?}: {}", browser_id, reason);
        if let Some(bt) = backtrace {
            error!("Backtrace: {}", bt);
        }
        
        // TODO: Handle browser crash recovery
    }

    fn prompt_permission(&self, browser_id: BrowserId, prompt: PermissionPrompt) -> PromptResult {
        info!("Permission prompt for browser {:?}: {:?}", browser_id, prompt);
        
        // For now, allow most permissions in development
        match prompt.permission_request {
            PermissionRequest::Camera => PromptResult::Allow,
            PermissionRequest::Microphone => PromptResult::Allow,
            PermissionRequest::Geolocation => PromptResult::Allow,
            PermissionRequest::Notifications => PromptResult::Allow,
            PermissionRequest::PersistentStorage => PromptResult::Allow,
            _ => PromptResult::Deny,
        }
    }

    fn show_context_menu(&self, browser_id: BrowserId, title: Option<String>, items: Vec<String>) {
        debug!("Context menu for browser {:?}: {:?}", browser_id, items);
        // TODO: Implement context menu
    }

    fn on_devtools_started(&self, browser_id: BrowserId, port: u16) {
        info!("DevTools started for browser {:?} on port {}", browser_id, port);
        // TODO: Notify frontend about DevTools availability
    }

    fn on_devtools_stopped(&self, browser_id: BrowserId) {
        info!("DevTools stopped for browser: {:?}", browser_id);
    }

    fn on_media_session_metadata(&self, browser_id: BrowserId, title: String, artist: String, album: String) {
        debug!("Media session metadata for browser {:?}: {} - {} ({})", browser_id, title, artist, album);
        // TODO: Update media controls
    }

    fn on_media_session_playback_state_change(&self, browser_id: BrowserId, state: servo::embedder_traits::MediaSessionPlaybackState) {
        debug!("Media playback state changed for browser {:?}: {:?}", browser_id, state);
        
        let tab_id = format!("{:?}", browser_id); // TODO: Map browser_id to tab_id
        let is_playing = matches!(state, servo::embedder_traits::MediaSessionPlaybackState::Playing);
        self.send_event(MemoriEvent::AudioStateChanged(tab_id, is_playing));
    }

    fn on_media_session_set_position_state(&self, browser_id: BrowserId, duration: f64, position: f64, playback_rate: f64) {
        debug!("Media position state for browser {:?}: pos={:.2}s, dur={:.2}s, rate={:.2}", 
               browser_id, position, duration, playback_rate);
        // TODO: Update media position controls
    }
}

struct MemoriEventLoopWaker {
    // TODO: Implement proper event loop waking mechanism
}

impl MemoriEventLoopWaker {
    fn new() -> Self {
        Self {}
    }
}

impl EventLoopWaker for MemoriEventLoopWaker {
    fn wake(&self) {
        // TODO: Wake up the event loop
    }

    fn clone_box(&self) -> Box<dyn EventLoopWaker> {
        Box::new(MemoriEventLoopWaker::new())
    }
}

impl PermissionManager {
    fn grant_permission(&self, url: ServoUrl, permission: PermissionRequest) {
        let mut permissions = self.granted_permissions.write();
        permissions.entry(url).or_insert_with(Vec::new).push(permission);
    }

    fn has_permission(&self, url: &ServoUrl, permission: &PermissionRequest) -> bool {
        self.granted_permissions
            .read()
            .get(url)
            .map(|perms| perms.contains(permission))
            .unwrap_or(false)
    }

    fn revoke_permission(&self, url: &ServoUrl, permission: &PermissionRequest) {
        if let Some(perms) = self.granted_permissions.write().get_mut(url) {
            perms.retain(|p| p != permission);
        }
    }
}
