use std::collections::HashMap;
use anyhow::Result;
use fuzzy_matcher::{FuzzyMatcher, skim::SkimMatcherV2};
use serde::{Deserialize, Serialize};
use crate::browser::Tab;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchResult {
    pub tab: Tab,
    pub score: f32,
    pub match_type: MatchType,
    pub matched_text: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum MatchType {
    Title,
    Url,
    Content,
    Domain,
    ExactMatch,
    FuzzyMatch,
}

pub struct SearchEngine {
    fuzzy_matcher: SkimMatcherV2,
    search_index: HashMap<String, Vec<String>>, // tab_id -> searchable_terms
    domain_groups: HashMap<String, Vec<String>>, // domain -> tab_ids
}

impl SearchEngine {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            fuzzy_matcher: SkimMatcherV2::default(),
            search_index: HashMap::new(),
            domain_groups: HashMap::new(),
        })
    }

    pub async fn search_tabs(&self, tabs: &[Tab], query: &str) -> Vec<Tab> {
        let mut results = Vec::new();
        let query_lower = query.to_lowercase();

        for tab in tabs {
            let mut score = 0.0;
            let mut match_found = false;

            // Exact title match (highest priority)
            if tab.title.to_lowercase().contains(&query_lower) {
                score += 100.0;
                match_found = true;
            }

            // Exact URL match
            if tab.url.to_lowercase().contains(&query_lower) {
                score += 80.0;
                match_found = true;
            }

            // Fuzzy title match
            if let Some(fuzzy_score) = self.fuzzy_matcher.fuzzy_match(&tab.title.to_lowercase(), &query_lower) {
                if fuzzy_score > 30 {
                    score += fuzzy_score as f32 * 0.5;
                    match_found = true;
                }
            }

            // Fuzzy URL match
            if let Some(fuzzy_score) = self.fuzzy_matcher.fuzzy_match(&tab.url.to_lowercase(), &query_lower) {
                if fuzzy_score > 30 {
                    score += fuzzy_score as f32 * 0.3;
                    match_found = true;
                }
            }

            // Domain match
            if let Ok(parsed_url) = url::Url::parse(&tab.url) {
                if let Some(domain) = parsed_url.domain() {
                    if domain.to_lowercase().contains(&query_lower) {
                        score += 60.0;
                        match_found = true;
                    }
                }
            }

            // Boost score for recently accessed tabs
            let time_boost = self.calculate_time_boost(tab.last_accessed);
            score += time_boost;

            // Boost score for frequently visited tabs
            let frequency_boost = (tab.visit_count as f32).min(10.0) * 2.0;
            score += frequency_boost;

            // Boost score for active tab
            if tab.is_active {
                score += 20.0;
            }

            // Boost score for audio-playing tabs if searching for "audio", "sound", "music", etc.
            if tab.is_playing_audio && self.is_audio_related_query(&query_lower) {
                score += 50.0;
                match_found = true;
            }

            if match_found && score > 0.0 {
                results.push((tab.clone(), score));
            }
        }

        // Sort by score (descending) and return tabs
        results.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        results.into_iter().map(|(tab, _)| tab).collect()
    }

    fn calculate_time_boost(&self, last_accessed: i64) -> f32 {
        let now = chrono::Utc::now().timestamp_millis();
        let time_diff = now - last_accessed;
        
        // Boost recent tabs (within last hour gets max boost)
        let hours_ago = time_diff as f32 / (1000.0 * 60.0 * 60.0);
        
        if hours_ago < 1.0 {
            20.0 * (1.0 - hours_ago)
        } else if hours_ago < 24.0 {
            10.0 * (1.0 - hours_ago / 24.0)
        } else {
            0.0
        }
    }

    fn is_audio_related_query(&self, query: &str) -> bool {
        let audio_keywords = [
            "audio", "sound", "music", "video", "play", "playing", 
            "youtube", "spotify", "netflix", "media", "volume"
        ];
        
        audio_keywords.iter().any(|&keyword| query.contains(keyword))
    }

    pub async fn search_by_domain(&self, tabs: &[Tab], domain: &str) -> Vec<Tab> {
        let domain_lower = domain.to_lowercase();
        
        tabs.iter()
            .filter(|tab| {
                if let Ok(parsed_url) = url::Url::parse(&tab.url) {
                    if let Some(tab_domain) = parsed_url.domain() {
                        return tab_domain.to_lowercase().contains(&domain_lower);
                    }
                }
                false
            })
            .cloned()
            .collect()
    }

    pub async fn find_audio_tabs(&self, tabs: &[Tab]) -> Vec<Tab> {
        tabs.iter()
            .filter(|tab| tab.is_playing_audio)
            .cloned()
            .collect()
    }

    pub async fn find_duplicate_tabs(&self, tabs: &[Tab]) -> Vec<Vec<Tab>> {
        let mut url_groups: HashMap<String, Vec<Tab>> = HashMap::new();
        
        for tab in tabs {
            // Normalize URL for comparison (remove fragments, some query params)
            let normalized_url = self.normalize_url(&tab.url);
            url_groups.entry(normalized_url).or_insert_with(Vec::new).push(tab.clone());
        }

        // Return groups with more than one tab
        url_groups.into_values()
            .filter(|group| group.len() > 1)
            .collect()
    }

    fn normalize_url(&self, url: &str) -> String {
        if let Ok(mut parsed_url) = url::Url::parse(url) {
            // Remove fragment
            parsed_url.set_fragment(None);
            
            // Remove common tracking parameters
            let tracking_params = ["utm_source", "utm_medium", "utm_campaign", "fbclid", "gclid"];
            let mut query_pairs: Vec<(String, String)> = parsed_url.query_pairs()
                .filter(|(key, _)| !tracking_params.contains(&key.as_ref()))
                .map(|(k, v)| (k.to_string(), v.to_string()))
                .collect();
            
            // Sort query parameters for consistent comparison
            query_pairs.sort_by(|a, b| a.0.cmp(&b.0));
            
            // Rebuild query string
            if query_pairs.is_empty() {
                parsed_url.set_query(None);
            } else {
                let query_string = query_pairs.iter()
                    .map(|(k, v)| format!("{}={}", k, v))
                    .collect::<Vec<_>>()
                    .join("&");
                parsed_url.set_query(Some(&query_string));
            }
            
            parsed_url.to_string()
        } else {
            url.to_string()
        }
    }

    pub async fn find_old_tabs(&self, tabs: &[Tab], hours_threshold: i64) -> Vec<Tab> {
        let threshold = chrono::Utc::now().timestamp_millis() - (hours_threshold * 60 * 60 * 1000);
        
        tabs.iter()
            .filter(|tab| !tab.is_active && tab.last_accessed < threshold)
            .cloned()
            .collect()
    }

    pub async fn group_tabs_by_domain(&self, tabs: &[Tab]) -> HashMap<String, Vec<Tab>> {
        let mut domain_groups: HashMap<String, Vec<Tab>> = HashMap::new();
        
        for tab in tabs {
            let domain = if let Ok(parsed_url) = url::Url::parse(&tab.url) {
                parsed_url.domain().unwrap_or("unknown").to_string()
            } else {
                "invalid".to_string()
            };
            
            domain_groups.entry(domain).or_insert_with(Vec::new).push(tab.clone());
        }

        domain_groups
    }

    pub async fn find_tabs_by_title_pattern(&self, tabs: &[Tab], pattern: &str) -> Vec<Tab> {
        let pattern_lower = pattern.to_lowercase();
        
        tabs.iter()
            .filter(|tab| {
                let title_lower = tab.title.to_lowercase();
                
                // Check for various patterns
                if pattern.starts_with('/') && pattern.ends_with('/') {
                    // Regex pattern (simplified)
                    let regex_pattern = &pattern[1..pattern.len()-1];
                    title_lower.contains(regex_pattern)
                } else if pattern.contains('*') {
                    // Wildcard pattern
                    let parts: Vec<&str> = pattern.split('*').collect();
                    if parts.len() == 2 {
                        title_lower.starts_with(&parts[0].to_lowercase()) && 
                        title_lower.ends_with(&parts[1].to_lowercase())
                    } else {
                        title_lower.contains(&pattern_lower)
                    }
                } else {
                    // Simple substring match
                    title_lower.contains(&pattern_lower)
                }
            })
            .cloned()
            .collect()
    }

    pub async fn get_search_suggestions(&self, query: &str, tabs: &[Tab]) -> Vec<String> {
        let mut suggestions = Vec::new();
        let query_lower = query.to_lowercase();

        // Collect unique domains
        let mut domains = std::collections::HashSet::new();
        for tab in tabs {
            if let Ok(parsed_url) = url::Url::parse(&tab.url) {
                if let Some(domain) = parsed_url.domain() {
                    if domain.to_lowercase().contains(&query_lower) {
                        domains.insert(domain.to_string());
                    }
                }
            }
        }

        // Add domain suggestions
        for domain in domains {
            suggestions.push(format!("domain:{}", domain));
        }

        // Add common search patterns
        if query_lower.len() > 2 {
            suggestions.push(format!("title:{}", query));
            suggestions.push(format!("url:{}", query));
        }

        // Add audio-related suggestions if applicable
        if self.is_audio_related_query(&query_lower) {
            suggestions.push("audio:playing".to_string());
            suggestions.push("tabs:with-sound".to_string());
        }

        // Limit suggestions
        suggestions.truncate(8);
        suggestions
    }

    pub async fn build_search_index(&mut self, tabs: &[Tab]) -> Result<()> {
        self.search_index.clear();
        self.domain_groups.clear();

        for tab in tabs {
            // Build searchable terms for each tab
            let mut terms = Vec::new();
            
            // Add title words
            for word in tab.title.split_whitespace() {
                if word.len() > 2 {
                    terms.push(word.to_lowercase());
                }
            }

            // Add URL components
            if let Ok(parsed_url) = url::Url::parse(&tab.url) {
                if let Some(domain) = parsed_url.domain() {
                    terms.push(domain.to_lowercase());
                    
                    // Group by domain
                    self.domain_groups
                        .entry(domain.to_string())
                        .or_insert_with(Vec::new)
                        .push(tab.id.clone());
                }

                // Add path segments
                if let Some(segments) = parsed_url.path_segments() {
                    for segment in segments {
                    if segment.len() > 2 && !segment.chars().all(|c| c.is_numeric()) {
                        terms.push(segment.to_lowercase());
                    }
                    }
                }
            }

            self.search_index.insert(tab.id.clone(), terms);
        }

        Ok(())
    }
}
