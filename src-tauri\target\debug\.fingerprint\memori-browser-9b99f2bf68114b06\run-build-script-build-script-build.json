{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9555057514056860554, "build_script_build", false, 10982097748941982393]], "local": [{"RerunIfChanged": {"output": "debug\\build\\memori-browser-9b99f2bf68114b06\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}