use anyhow::Result;
use std::time::{SystemTime, UNIX_EPOCH};

pub fn current_timestamp() -> i64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as i64
}

pub fn format_duration(milliseconds: i64) -> String {
    let seconds = milliseconds / 1000;
    let minutes = seconds / 60;
    let hours = minutes / 60;
    let days = hours / 24;

    if days > 0 {
        format!("{} days ago", days)
    } else if hours > 0 {
        format!("{} hours ago", hours)
    } else if minutes > 0 {
        format!("{} minutes ago", minutes)
    } else {
        "Just now".to_string()
    }
}

pub fn extract_domain_from_url(url: &str) -> Option<String> {
    if let Ok(parsed_url) = url::Url::parse(url) {
        parsed_url.domain().map(|d| d.to_string())
    } else {
        None
    }
}

pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '<' | '>' | ':' | '"' | '/' | '\\' | '|' | '?' | '*' => '_',
            _ => c,
        })
        .collect()
}

pub fn calculate_similarity(text1: &str, text2: &str) -> f32 {
    let text1_lower = text1.to_lowercase();
    let text2_lower = text2.to_lowercase();
    
    if text1_lower == text2_lower {
        return 1.0;
    }
    
    let words1: std::collections::HashSet<&str> = text1_lower.split_whitespace().collect();
    let words2: std::collections::HashSet<&str> = text2_lower.split_whitespace().collect();
    
    let intersection = words1.intersection(&words2).count();
    let union = words1.union(&words2).count();
    
    if union == 0 {
        0.0
    } else {
        intersection as f32 / union as f32
    }
}

pub fn truncate_string(s: &str, max_length: usize) -> String {
    if s.len() <= max_length {
        s.to_string()
    } else {
        format!("{}...", &s[..max_length.saturating_sub(3)])
    }
}

pub fn is_valid_url(url: &str) -> bool {
    url::Url::parse(url).is_ok()
}

pub fn normalize_search_query(query: &str) -> String {
    query
        .to_lowercase()
        .trim()
        .split_whitespace()
        .collect::<Vec<_>>()
        .join(" ")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_domain() {
        assert_eq!(
            extract_domain_from_url("https://www.example.com/path"),
            Some("www.example.com".to_string())
        );
        assert_eq!(extract_domain_from_url("invalid-url"), None);
    }

    #[test]
    fn test_calculate_similarity() {
        assert_eq!(calculate_similarity("hello world", "hello world"), 1.0);
        assert_eq!(calculate_similarity("hello", "world"), 0.0);
        assert!(calculate_similarity("hello world", "hello") > 0.0);
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("file<name>.txt"), "file_name_.txt");
    }
}
