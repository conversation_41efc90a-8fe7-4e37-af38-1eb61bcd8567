{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 15389663814530670383, "deps": [[5103565458935487, "futures_io", false, 9440358948715317043], [1615478164327904835, "pin_utils", false, 9088319102551614221], [1906322745568073236, "pin_project_lite", false, 9120953935551481106], [5451793922601807560, "slab", false, 11981280055167465404], [7013762810557009322, "futures_sink", false, 1175717568269615725], [7620660491849607393, "futures_core", false, 16471636417681934567], [10565019901765856648, "futures_macro", false, 2167939452339623277], [15932120279885307830, "memchr", false, 3149709465132557017], [16240732885093539806, "futures_task", false, 1675188089003574034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a6ddd4e1f5bbc96b\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}