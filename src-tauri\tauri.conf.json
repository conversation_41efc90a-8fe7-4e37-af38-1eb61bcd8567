{"productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "identifier": "com.memori.browser", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "../dist", "devUrl": "http://localhost:5173"}, "app": {"windows": [{"title": "<PERSON><PERSON><PERSON>", "width": 1400, "height": 900, "resizable": true, "fullscreen": false, "center": true, "minWidth": 800, "minHeight": 600, "focus": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/icon.ico"]}, "plugins": {}}