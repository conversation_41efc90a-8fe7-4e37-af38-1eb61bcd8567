{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:5173", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true, "execute": true}, "window": {"all": false, "create": true, "center": true, "setResizable": true, "setTitle": true, "maximize": true, "unmaximize": true, "minimize": true, "unminimize": true, "show": true, "hide": true, "close": true, "setSize": true, "setPosition": true, "setFullscreen": true, "setFocus": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "createDir": true, "scope": ["$APPDATA", "$APPDATA/**", "$RESOURCE/**", "$APP/**"]}, "path": {"all": true}, "os": {"all": true}, "http": {"all": false, "request": true, "scope": ["https://**", "http://**"]}, "notification": {"all": true}, "dialog": {"all": false, "open": true, "save": true, "message": true}, "clipboard": {"all": false, "writeText": true, "readText": true}, "globalShortcut": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.memori.browser", "icon": ["icons/icon.ico"]}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"title": "<PERSON><PERSON><PERSON>", "width": 1400, "height": 900, "resizable": true, "fullscreen": false, "center": true, "minWidth": 800, "minHeight": 600, "focus": true, "url": "index.html", "label": "main"}]}}