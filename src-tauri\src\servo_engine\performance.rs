use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::Result;
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error, debug};
use tokio::time::interval;

use servo::BrowserId;
use servo::servo_url::ServoUrl;

use super::{ServoPerformanceMetrics, EnergyImpact};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoPerformanceConfig {
    pub enable_profiling: bool,
    pub enable_memory_profiling: bool,
    pub enable_time_profiling: bool,
    pub enable_layout_profiling: bool,
    pub enable_paint_profiling: bool,
    pub enable_script_profiling: bool,
    pub profiling_interval_ms: u64,
    pub memory_warning_threshold_mb: f64,
    pub memory_critical_threshold_mb: f64,
    pub cpu_warning_threshold_percent: f32,
    pub frame_rate_warning_threshold: f32,
}

impl Default for ServoPerformanceConfig {
    fn default() -> Self {
        Self {
            enable_profiling: true,
            enable_memory_profiling: true,
            enable_time_profiling: true,
            enable_layout_profiling: true,
            enable_paint_profiling: true,
            enable_script_profiling: true,
            profiling_interval_ms: 1000,
            memory_warning_threshold_mb: 512.0,
            memory_critical_threshold_mb: 1024.0,
            cpu_warning_threshold_percent: 80.0,
            frame_rate_warning_threshold: 30.0,
        }
    }
}

#[derive(Debug, Clone)]
struct TabPerformanceState {
    tab_id: String,
    browser_id: BrowserId,
    url: ServoUrl,
    start_time: Instant,
    last_update: Instant,
    metrics_history: Vec<ServoPerformanceMetrics>,
    layout_samples: Vec<f64>,
    paint_samples: Vec<f64>,
    script_samples: Vec<f64>,
    memory_samples: Vec<f64>,
    frame_times: Vec<Duration>,
    is_active: bool,
    is_visible: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoPerformanceAlert {
    pub alert_type: PerformanceAlertType,
    pub tab_id: String,
    pub description: String,
    pub severity: AlertSeverity,
    pub current_value: f64,
    pub threshold_value: f64,
    pub timestamp: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PerformanceAlertType {
    HighMemoryUsage,
    HighCPUUsage,
    LowFrameRate,
    SlowLayout,
    SlowPaint,
    SlowScript,
    MemoryLeak,
    InfiniteLoop,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

pub struct ServoPerformanceMonitor {
    config: ServoPerformanceConfig,
    tab_states: Arc<RwLock<HashMap<String, TabPerformanceState>>>,
    global_metrics: Arc<RwLock<GlobalServoMetrics>>,
    alerts: Arc<RwLock<Vec<ServoPerformanceAlert>>>,
    monitoring_active: bool,
}

#[derive(Debug, Default)]
struct GlobalServoMetrics {
    total_memory_mb: f64,
    total_cpu_percent: f32,
    active_tabs: u32,
    visible_tabs: u32,
    average_frame_rate: f32,
    total_layout_time: f64,
    total_paint_time: f64,
    total_script_time: f64,
}

impl ServoPerformanceMonitor {
    pub async fn new() -> Result<Self> {
        let config = ServoPerformanceConfig::default();
        
        Ok(Self {
            config,
            tab_states: Arc::new(RwLock::new(HashMap::new())),
            global_metrics: Arc::new(RwLock::new(GlobalServoMetrics::default())),
            alerts: Arc::new(RwLock::new(Vec::new())),
            monitoring_active: false,
        })
    }

    pub async fn start_monitoring(&self, tab_id: &str) -> Result<()> {
        let state = TabPerformanceState {
            tab_id: tab_id.to_string(),
            browser_id: BrowserId::new(), // TODO: Get actual browser ID
            url: ServoUrl::parse("about:blank")?, // TODO: Get actual URL
            start_time: Instant::now(),
            last_update: Instant::now(),
            metrics_history: Vec::new(),
            layout_samples: Vec::new(),
            paint_samples: Vec::new(),
            script_samples: Vec::new(),
            memory_samples: Vec::new(),
            frame_times: Vec::new(),
            is_active: true,
            is_visible: true,
        };

        self.tab_states.write().insert(tab_id.to_string(), state);
        
        // Start background monitoring if not already running
        if !self.monitoring_active {
            self.start_background_monitoring().await?;
        }

        info!("Started Servo performance monitoring for tab: {}", tab_id);
        Ok(())
    }

    pub async fn stop_monitoring(&self, tab_id: &str) -> Result<()> {
        if let Some(state) = self.tab_states.write().remove(tab_id) {
            let duration = state.start_time.elapsed();
            info!(
                "Stopped monitoring tab: {} (duration: {:?}, samples: {})",
                tab_id,
                duration,
                state.metrics_history.len()
            );
        }
        Ok(())
    }

    pub async fn update_metrics(&self, tab_id: &str, metrics: ServoPerformanceMetrics) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            // Update metrics history
            state.metrics_history.push(metrics.clone());
            state.last_update = Instant::now();

            // Update sample arrays
            state.layout_samples.push(metrics.layout_time_ms);
            state.paint_samples.push(metrics.paint_time_ms);
            state.script_samples.push(metrics.script_time_ms);
            state.memory_samples.push(metrics.memory_usage_mb);

            // Keep only last 100 samples to prevent memory bloat
            if state.metrics_history.len() > 100 {
                state.metrics_history.drain(0..50);
                state.layout_samples.drain(0..50);
                state.paint_samples.drain(0..50);
                state.script_samples.drain(0..50);
                state.memory_samples.drain(0..50);
            }

            // Check for performance issues
            self.analyze_performance_issues(tab_id, &metrics).await?;
        }
        Ok(())
    }

    pub async fn record_frame_time(&self, tab_id: &str, frame_time: Duration) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.frame_times.push(frame_time);
            
            // Keep only last 60 frame times (1 second at 60fps)
            if state.frame_times.len() > 60 {
                state.frame_times.drain(0..30);
            }

            // Calculate current frame rate
            if state.frame_times.len() >= 10 {
                let avg_frame_time = state.frame_times.iter()
                    .rev()
                    .take(10)
                    .map(|t| t.as_secs_f32())
                    .sum::<f32>() / 10.0;
                
                let frame_rate = 1.0 / avg_frame_time;
                
                // Check for low frame rate
                if frame_rate < self.config.frame_rate_warning_threshold {
                    self.create_alert(
                        PerformanceAlertType::LowFrameRate,
                        tab_id,
                        format!("Low frame rate: {:.1} FPS", frame_rate),
                        AlertSeverity::Warning,
                        frame_rate as f64,
                        self.config.frame_rate_warning_threshold as f64,
                    ).await;
                }
            }
        }
        Ok(())
    }

    pub async fn set_tab_visibility(&self, tab_id: &str, is_visible: bool) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.is_visible = is_visible;
            debug!("Tab {} visibility changed: {}", tab_id, is_visible);
        }
        Ok(())
    }

    pub async fn set_tab_active(&self, tab_id: &str, is_active: bool) -> Result<()> {
        if let Some(state) = self.tab_states.write().get_mut(tab_id) {
            state.is_active = is_active;
            debug!("Tab {} active state changed: {}", tab_id, is_active);
        }
        Ok(())
    }

    pub async fn get_tab_metrics(&self, tab_id: &str) -> Option<ServoPerformanceMetrics> {
        self.tab_states
            .read()
            .get(tab_id)?
            .metrics_history
            .last()
            .cloned()
    }

    pub async fn get_tab_performance_summary(&self, tab_id: &str) -> Option<TabPerformanceSummary> {
        let state = self.tab_states.read();
        let tab_state = state.get(tab_id)?;

        if tab_state.metrics_history.is_empty() {
            return None;
        }

        let avg_layout_time = tab_state.layout_samples.iter().sum::<f64>() / tab_state.layout_samples.len() as f64;
        let avg_paint_time = tab_state.paint_samples.iter().sum::<f64>() / tab_state.paint_samples.len() as f64;
        let avg_script_time = tab_state.script_samples.iter().sum::<f64>() / tab_state.script_samples.len() as f64;
        let avg_memory = tab_state.memory_samples.iter().sum::<f64>() / tab_state.memory_samples.len() as f64;

        let max_memory = tab_state.memory_samples.iter().fold(0.0, |a, &b| a.max(b));
        let min_memory = tab_state.memory_samples.iter().fold(f64::INFINITY, |a, &b| a.min(b));

        Some(TabPerformanceSummary {
            tab_id: tab_id.to_string(),
            uptime_seconds: tab_state.start_time.elapsed().as_secs(),
            total_samples: tab_state.metrics_history.len(),
            avg_layout_time_ms: avg_layout_time,
            avg_paint_time_ms: avg_paint_time,
            avg_script_time_ms: avg_script_time,
            avg_memory_mb: avg_memory,
            max_memory_mb: max_memory,
            min_memory_mb: min_memory,
            current_frame_rate: tab_state.frame_times.last()
                .map(|t| 1.0 / t.as_secs_f32())
                .unwrap_or(0.0),
            energy_impact: self.calculate_energy_impact(&tab_state.metrics_history.last().unwrap_or(&ServoPerformanceMetrics::default())),
        })
    }

    pub async fn get_global_metrics(&self) -> GlobalServoMetrics {
        self.global_metrics.read().clone()
    }

    pub async fn get_recent_alerts(&self, limit: usize) -> Vec<ServoPerformanceAlert> {
        let alerts = self.alerts.read();
        alerts.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    async fn start_background_monitoring(&self) -> Result<()> {
        let tab_states = Arc::clone(&self.tab_states);
        let global_metrics = Arc::clone(&self.global_metrics);
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_millis(config.profiling_interval_ms));
            
            loop {
                interval.tick().await;
                
                // Update global metrics
                let mut total_memory = 0.0;
                let mut total_layout_time = 0.0;
                let mut total_paint_time = 0.0;
                let mut total_script_time = 0.0;
                let mut active_tabs = 0;
                let mut visible_tabs = 0;
                let mut frame_rates = Vec::new();

                {
                    let states = tab_states.read();
                    for state in states.values() {
                        if let Some(latest_metrics) = state.metrics_history.last() {
                            total_memory += latest_metrics.memory_usage_mb;
                            total_layout_time += latest_metrics.layout_time_ms;
                            total_paint_time += latest_metrics.paint_time_ms;
                            total_script_time += latest_metrics.script_time_ms;
                            frame_rates.push(latest_metrics.frame_rate);
                        }
                        
                        if state.is_active {
                            active_tabs += 1;
                        }
                        if state.is_visible {
                            visible_tabs += 1;
                        }
                    }
                }

                let avg_frame_rate = if !frame_rates.is_empty() {
                    frame_rates.iter().sum::<f32>() / frame_rates.len() as f32
                } else {
                    0.0
                };

                // Update global metrics
                {
                    let mut global = global_metrics.write();
                    global.total_memory_mb = total_memory;
                    global.active_tabs = active_tabs;
                    global.visible_tabs = visible_tabs;
                    global.average_frame_rate = avg_frame_rate;
                    global.total_layout_time = total_layout_time;
                    global.total_paint_time = total_paint_time;
                    global.total_script_time = total_script_time;
                }

                debug!(
                    "Global metrics - Memory: {:.1} MB, Active tabs: {}, Frame rate: {:.1} FPS",
                    total_memory, active_tabs, avg_frame_rate
                );
            }
        });

        Ok(())
    }

    async fn analyze_performance_issues(&self, tab_id: &str, metrics: &ServoPerformanceMetrics) -> Result<()> {
        // Memory usage warnings
        if metrics.memory_usage_mb > self.config.memory_warning_threshold_mb {
            let severity = if metrics.memory_usage_mb > self.config.memory_critical_threshold_mb {
                AlertSeverity::Critical
            } else {
                AlertSeverity::Warning
            };

            self.create_alert(
                PerformanceAlertType::HighMemoryUsage,
                tab_id,
                format!("High memory usage: {:.1} MB", metrics.memory_usage_mb),
                severity,
                metrics.memory_usage_mb,
                self.config.memory_warning_threshold_mb,
            ).await;
        }

        // Layout performance warnings
        if metrics.layout_time_ms > 16.0 { // More than one frame at 60fps
            self.create_alert(
                PerformanceAlertType::SlowLayout,
                tab_id,
                format!("Slow layout: {:.1} ms", metrics.layout_time_ms),
                AlertSeverity::Warning,
                metrics.layout_time_ms,
                16.0,
            ).await;
        }

        // Paint performance warnings
        if metrics.paint_time_ms > 16.0 {
            self.create_alert(
                PerformanceAlertType::SlowPaint,
                tab_id,
                format!("Slow paint: {:.1} ms", metrics.paint_time_ms),
                AlertSeverity::Warning,
                metrics.paint_time_ms,
                16.0,
            ).await;
        }

        // Script performance warnings
        if metrics.script_time_ms > 50.0 { // Long-running script
            self.create_alert(
                PerformanceAlertType::SlowScript,
                tab_id,
                format!("Long-running script: {:.1} ms", metrics.script_time_ms),
                AlertSeverity::Warning,
                metrics.script_time_ms,
                50.0,
            ).await;
        }

        Ok(())
    }

    async fn create_alert(
        &self,
        alert_type: PerformanceAlertType,
        tab_id: &str,
        description: String,
        severity: AlertSeverity,
        current_value: f64,
        threshold_value: f64,
    ) {
        let alert = ServoPerformanceAlert {
            alert_type,
            tab_id: tab_id.to_string(),
            description: description.clone(),
            severity: severity.clone(),
            current_value,
            threshold_value,
            timestamp: chrono::Utc::now().timestamp_millis(),
        };

        self.alerts.write().push(alert);

        match severity {
            AlertSeverity::Critical => error!("CRITICAL PERFORMANCE ALERT: {}", description),
            AlertSeverity::Warning => warn!("PERFORMANCE WARNING: {}", description),
            AlertSeverity::Info => info!("PERFORMANCE INFO: {}", description),
        }
    }

    fn calculate_energy_impact(&self, metrics: &ServoPerformanceMetrics) -> EnergyImpact {
        let score = (metrics.memory_usage_mb / 1000.0) * 0.3
            + (metrics.layout_time_ms / 100.0) * 0.2
            + (metrics.paint_time_ms / 100.0) * 0.2
            + (metrics.script_time_ms / 100.0) * 0.2
            + ((60.0 - metrics.frame_rate) / 60.0) as f64 * 0.1;

        match score {
            s if s < 0.2 => EnergyImpact::Low,
            s if s < 0.5 => EnergyImpact::Medium,
            s if s < 0.8 => EnergyImpact::High,
            _ => EnergyImpact::VeryHigh,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct TabPerformanceSummary {
    pub tab_id: String,
    pub uptime_seconds: u64,
    pub total_samples: usize,
    pub avg_layout_time_ms: f64,
    pub avg_paint_time_ms: f64,
    pub avg_script_time_ms: f64,
    pub avg_memory_mb: f64,
    pub max_memory_mb: f64,
    pub min_memory_mb: f64,
    pub current_frame_rate: f32,
    pub energy_impact: EnergyImpact,
}
