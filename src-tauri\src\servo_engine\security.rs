use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error, debug};

use servo::servo_url::ServoUrl;
use servo::script_traits::{DocumentActivity, LoadData};

use super::ServoConfig;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServoSecurityLevel {
    Strict,      // Maximum security, may break some sites
    Balanced,    // Good security with compatibility
    Permissive,  // Minimal restrictions
    Custom,      // User-defined rules
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoSecurityPolicy {
    pub level: ServoSecurityLevel,
    pub enable_mixed_content_blocking: bool,
    pub enable_csp_enforcement: bool,
    pub enable_cors_enforcement: bool,
    pub enable_referrer_policy: bool,
    pub enable_https_only: bool,
    pub enable_hsts: bool,
    pub enable_certificate_transparency: bool,
    pub blocked_schemes: HashSet<String>,
    pub blocked_domains: HashSet<String>,
    pub trusted_domains: HashSet<String>,
    pub custom_csp_rules: HashMap<String, String>,
}

impl Default for ServoSecurityPolicy {
    fn default() -> Self {
        let mut blocked_schemes = HashSet::new();
        blocked_schemes.insert("ftp".to_string());
        blocked_schemes.insert("gopher".to_string());
        
        Self {
            level: ServoSecurityLevel::Balanced,
            enable_mixed_content_blocking: true,
            enable_csp_enforcement: true,
            enable_cors_enforcement: true,
            enable_referrer_policy: true,
            enable_https_only: false,
            enable_hsts: true,
            enable_certificate_transparency: true,
            blocked_schemes,
            blocked_domains: HashSet::new(),
            trusted_domains: HashSet::new(),
            custom_csp_rules: HashMap::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoSecurityThreat {
    pub threat_type: ServoThreatType,
    pub url: ServoUrl,
    pub description: String,
    pub severity: ThreatSeverity,
    pub timestamp: i64,
    pub blocked: bool,
    pub context: ThreatContext,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServoThreatType {
    MaliciousScript,
    XSSAttempt,
    CSRFAttempt,
    MixedContent,
    InsecureConnection,
    CertificateError,
    MaliciousDownload,
    PhishingAttempt,
    TrackingScript,
    CryptojackingScript,
    DataExfiltration,
    ClickjackingAttempt,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThreatSeverity {
    Info,
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatContext {
    pub source_url: Option<ServoUrl>,
    pub target_url: Option<ServoUrl>,
    pub request_method: Option<String>,
    pub content_type: Option<String>,
    pub user_agent: Option<String>,
}

pub struct ServoSecurityManager {
    policy: Arc<RwLock<ServoSecurityPolicy>>,
    threat_log: Arc<RwLock<Vec<ServoSecurityThreat>>>,
    csp_violations: Arc<RwLock<HashMap<ServoUrl, Vec<CSPViolation>>>>,
    certificate_cache: Arc<RwLock<HashMap<String, CertificateInfo>>>,
    hsts_cache: Arc<RwLock<HashMap<String, HSTSEntry>>>,
}

#[derive(Debug, Clone)]
struct CSPViolation {
    directive: String,
    blocked_uri: String,
    violated_directive: String,
    timestamp: i64,
}

#[derive(Debug, Clone)]
struct CertificateInfo {
    is_valid: bool,
    issuer: String,
    subject: String,
    not_before: i64,
    not_after: i64,
    fingerprint: String,
}

#[derive(Debug, Clone)]
struct HSTSEntry {
    max_age: u64,
    include_subdomains: bool,
    preload: bool,
    timestamp: i64,
}

impl ServoSecurityManager {
    pub async fn new(config: &ServoConfig) -> Result<Self> {
        let policy = ServoSecurityPolicy::default();
        
        let manager = Self {
            policy: Arc::new(RwLock::new(policy)),
            threat_log: Arc::new(RwLock::new(Vec::new())),
            csp_violations: Arc::new(RwLock::new(HashMap::new())),
            certificate_cache: Arc::new(RwLock::new(HashMap::new())),
            hsts_cache: Arc::new(RwLock::new(HashMap::new())),
        };

        // Load security databases
        manager.load_security_databases().await?;
        
        info!("Servo security manager initialized");
        Ok(manager)
    }

    pub async fn validate_url(&self, url: &ServoUrl) -> Result<()> {
        let policy = self.policy.read();
        
        // Check blocked schemes
        if policy.blocked_schemes.contains(url.scheme()) {
            return Err(anyhow!("Blocked scheme: {}", url.scheme()));
        }

        // Check blocked domains
        if let Some(domain) = url.domain() {
            if policy.blocked_domains.contains(domain) {
                return Err(anyhow!("Blocked domain: {}", domain));
            }
        }

        // HTTPS-only mode
        if policy.enable_https_only && url.scheme() == "http" {
            // Try to upgrade to HTTPS
            let mut https_url = url.clone();
            https_url.set_scheme("https").map_err(|_| anyhow!("Cannot upgrade to HTTPS"))?;
            
            warn!("Upgrading HTTP to HTTPS: {} -> {}", url, https_url);
            return Err(anyhow!("HTTP upgraded to HTTPS"));
        }

        // Check HSTS
        if let Some(domain) = url.domain() {
            if let Some(hsts_entry) = self.hsts_cache.read().get(domain) {
                if url.scheme() == "http" {
                    return Err(anyhow!("HSTS policy requires HTTPS for domain: {}", domain));
                }
            }
        }

        Ok(())
    }

    pub async fn validate_request(&self, load_data: &LoadData) -> Result<LoadData> {
        let mut validated_load_data = load_data.clone();
        let policy = self.policy.read();

        // Apply referrer policy
        if policy.enable_referrer_policy {
            validated_load_data = self.apply_referrer_policy(validated_load_data).await?;
        }

        // Check for mixed content
        if policy.enable_mixed_content_blocking {
            self.check_mixed_content(&validated_load_data).await?;
        }

        // Apply CORS if needed
        if policy.enable_cors_enforcement {
            self.validate_cors(&validated_load_data).await?;
        }

        Ok(validated_load_data)
    }

    pub fn generate_csp_header(&self, url: &ServoUrl) -> Option<String> {
        let policy = self.policy.read();
        
        if !policy.enable_csp_enforcement {
            return None;
        }

        // Check for custom CSP rules
        if let Some(domain) = url.domain() {
            if let Some(custom_csp) = policy.custom_csp_rules.get(domain) {
                return Some(custom_csp.clone());
            }
        }

        // Generate default CSP based on security level
        let csp = match policy.level {
            ServoSecurityLevel::Strict => {
                "default-src 'self'; \
                 script-src 'self'; \
                 style-src 'self' 'unsafe-inline'; \
                 img-src 'self' data: https:; \
                 connect-src 'self' https:; \
                 font-src 'self' https:; \
                 object-src 'none'; \
                 media-src 'self' https:; \
                 frame-src 'none'; \
                 worker-src 'self'; \
                 child-src 'self'; \
                 form-action 'self'; \
                 upgrade-insecure-requests; \
                 block-all-mixed-content;"
            }
            ServoSecurityLevel::Balanced => {
                "default-src 'self' https:; \
                 script-src 'self' 'unsafe-inline' https:; \
                 style-src 'self' 'unsafe-inline' https:; \
                 img-src 'self' data: https:; \
                 connect-src 'self' https: wss:; \
                 font-src 'self' https:; \
                 object-src 'self' https:; \
                 media-src 'self' https:; \
                 frame-src 'self' https:; \
                 worker-src 'self' https:; \
                 child-src 'self' https:; \
                 form-action 'self' https:; \
                 upgrade-insecure-requests;"
            }
            ServoSecurityLevel::Permissive => {
                "default-src *; \
                 script-src * 'unsafe-inline' 'unsafe-eval'; \
                 style-src * 'unsafe-inline'; \
                 img-src * data:; \
                 connect-src *; \
                 font-src *; \
                 object-src *; \
                 media-src *; \
                 frame-src *; \
                 worker-src *; \
                 child-src *;"
            }
            ServoSecurityLevel::Custom => {
                // Use default balanced policy for custom
                return self.generate_csp_header(url);
            }
        };

        Some(csp.to_string())
    }

    pub async fn report_csp_violation(&self, url: ServoUrl, violation: CSPViolation) {
        debug!("CSP violation reported for {}: {:?}", url, violation);
        
        self.csp_violations.write()
            .entry(url.clone())
            .or_insert_with(Vec::new)
            .push(violation.clone());

        // Log as security threat
        let threat = ServoSecurityThreat {
            threat_type: ServoThreatType::CSRFAttempt,
            url,
            description: format!("CSP violation: {}", violation.violated_directive),
            severity: ThreatSeverity::Medium,
            timestamp: chrono::Utc::now().timestamp_millis(),
            blocked: true,
            context: ThreatContext {
                source_url: None,
                target_url: None,
                request_method: None,
                content_type: None,
                user_agent: None,
            },
        };

        self.log_threat(threat).await;
    }

    pub async fn validate_certificate(&self, domain: &str, cert_data: &[u8]) -> Result<bool> {
        // TODO: Implement proper certificate validation
        // For now, return true for development
        
        let cert_info = CertificateInfo {
            is_valid: true,
            issuer: "Development CA".to_string(),
            subject: domain.to_string(),
            not_before: chrono::Utc::now().timestamp_millis(),
            not_after: chrono::Utc::now().timestamp_millis() + (365 * 24 * 60 * 60 * 1000),
            fingerprint: "dev-fingerprint".to_string(),
        };

        self.certificate_cache.write().insert(domain.to_string(), cert_info);
        
        Ok(true)
    }

    pub async fn update_hsts(&self, domain: &str, max_age: u64, include_subdomains: bool) {
        let hsts_entry = HSTSEntry {
            max_age,
            include_subdomains,
            preload: false,
            timestamp: chrono::Utc::now().timestamp_millis(),
        };

        self.hsts_cache.write().insert(domain.to_string(), hsts_entry);
        info!("Updated HSTS for domain: {} (max_age: {})", domain, max_age);
    }

    async fn apply_referrer_policy(&self, mut load_data: LoadData) -> Result<LoadData> {
        // Apply strict referrer policy
        // TODO: Implement proper referrer policy logic
        Ok(load_data)
    }

    async fn check_mixed_content(&self, load_data: &LoadData) -> Result<()> {
        // TODO: Implement mixed content detection
        // Check if HTTPS page is loading HTTP resources
        Ok(())
    }

    async fn validate_cors(&self, load_data: &LoadData) -> Result<()> {
        // TODO: Implement CORS validation
        Ok(())
    }

    async fn load_security_databases(&self) -> Result<()> {
        // TODO: Load threat databases, certificate transparency logs, etc.
        info!("Security databases loaded");
        Ok(())
    }

    async fn log_threat(&self, threat: ServoSecurityThreat) {
        let mut log = self.threat_log.write();
        log.push(threat.clone());

        // Keep only last 10000 entries
        if log.len() > 10000 {
            log.drain(0..5000);
        }

        match threat.severity {
            ThreatSeverity::Critical => error!("CRITICAL SECURITY THREAT: {}", threat.description),
            ThreatSeverity::High => error!("HIGH SECURITY THREAT: {}", threat.description),
            ThreatSeverity::Medium => warn!("MEDIUM SECURITY THREAT: {}", threat.description),
            ThreatSeverity::Low => info!("LOW SECURITY THREAT: {}", threat.description),
            ThreatSeverity::Info => debug!("SECURITY INFO: {}", threat.description),
        }
    }

    pub async fn get_security_report(&self) -> ServoSecurityReport {
        let threats = self.threat_log.read();
        let csp_violations = self.csp_violations.read();
        
        let total_threats = threats.len();
        let critical_threats = threats.iter().filter(|t| matches!(t.severity, ThreatSeverity::Critical)).count();
        let blocked_threats = threats.iter().filter(|t| t.blocked).count();
        let total_csp_violations = csp_violations.values().map(|v| v.len()).sum();

        ServoSecurityReport {
            total_threats,
            critical_threats,
            blocked_threats,
            total_csp_violations,
            hsts_entries: self.hsts_cache.read().len(),
            certificate_cache_size: self.certificate_cache.read().len(),
            security_level: self.policy.read().level.clone(),
        }
    }

    pub async fn set_security_policy(&self, policy: ServoSecurityPolicy) {
        *self.policy.write() = policy;
        info!("Security policy updated");
    }
}

#[derive(Debug, Serialize)]
pub struct ServoSecurityReport {
    pub total_threats: usize,
    pub critical_threats: usize,
    pub blocked_threats: usize,
    pub total_csp_violations: usize,
    pub hsts_entries: usize,
    pub certificate_cache_size: usize,
    pub security_level: ServoSecurityLevel,
}
