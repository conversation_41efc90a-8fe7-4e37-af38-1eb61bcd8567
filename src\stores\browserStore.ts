import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/tauri';

export interface Tab {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  is_active: boolean;
  is_loading: boolean;
  is_playing_audio: boolean;
  last_accessed: number;
  bookmark_suggested: boolean;
  visit_count: number;
}

export interface BrowserState {
  tabs: Tab[];
  activeTabId: string | null;
  searchQuery: string;
  isVoiceSearchActive: boolean;
  isChatSidebarOpen: boolean;
  audioTabs: string[];
  bookmarkSuggestions: string[];
  urlCompletions: Array<{
    display: string;
    url: string;
    confidence: number;
  }>;
}

export interface BrowserActions {
  // Tab Management
  createTab: (url?: string) => Promise<string>;
  closeTab: (tabId: string) => Promise<void>;
  switchTab: (tabId: string) => Promise<void>;
  updateTab: (tabId: string, updates: Partial<Tab>) => void;
  updateTabs: () => Promise<void>;
  
  // Audio Detection
  setTabAudioState: (tabId: string, isPlaying: boolean) => void;
  getAudioTabs: () => string[];
  
  // Smart Search & Navigation
  setSearchQuery: (query: string) => void;
  searchTabs: (query: string) => Tab[];
  getUrlCompletions: (input: string) => Promise<void>;
  navigateToUrl: (url: string) => Promise<void>;
  
  // Voice Features
  toggleVoiceSearch: () => void;
  startVoiceRecognition: () => Promise<string | undefined>;
  
  // Bookmarks & Intelligence
  suggestBookmark: (tabId: string) => void;
  autoBookmark: (tabId: string) => Promise<void>;
  getBookmarkSuggestions: () => string[];
  
  // Chat Sidebar
  toggleChatSidebar: () => void;
  sendChatMessage: (message: string) => Promise<string>;
  
  // Performance & Memory
  optimizeMemory: () => Promise<void>;
  preloadFrequentSites: () => Promise<void>;
}

export const useBrowserStore = create<BrowserState & BrowserActions>((set, get) => ({
  // Initial State
  tabs: [],
  activeTabId: null,
  searchQuery: '',
  isVoiceSearchActive: false,
  isChatSidebarOpen: false,
  audioTabs: [],
  bookmarkSuggestions: [],
  urlCompletions: [],

  // Tab Management
  createTab: async (url?: string) => {
    try {
      // Call the correct Tauri backend command
      const tabId = await invoke('create_new_tab', { url: url || null });
      console.log('✅ Tab created with ID:', tabId);

      // Create tab object for frontend state
      const newTab: Tab = {
        id: tabId as string,
        title: url ? 'Loading...' : 'New Tab',
        url: url || '',
        is_active: true,
        is_loading: !!url,
        is_playing_audio: false,
        last_accessed: Date.now(),
        bookmark_suggested: false,
        visit_count: 1,
      };

      set((state) => ({
        tabs: state.tabs.map(tab => ({ ...tab, is_active: false })).concat(newTab),
        activeTabId: newTab.id,
      }));

      return tabId as string;
    } catch (error) {
      console.error('Failed to create tab:', error);
      throw error;
    }
  },

  closeTab: async (tabId: string) => {
    try {
      const { tabs, activeTabId } = get();
      const tabIndex = tabs.findIndex(tab => tab.id === tabId);
      
      if (tabIndex === -1) return;

      const newTabs = tabs.filter(tab => tab.id !== tabId);
      let newActiveTabId = activeTabId;

      // If closing active tab, switch to adjacent tab
      if (activeTabId === tabId && newTabs.length > 0) {
        const nextIndex = Math.min(tabIndex, newTabs.length - 1);
        newActiveTabId = newTabs[nextIndex]?.id || null;
        if (newActiveTabId) {
          newTabs[nextIndex].is_active = true;
        }
      }

      set({
        tabs: newTabs,
        activeTabId: newActiveTabId,
        audioTabs: get().audioTabs.filter(id => id !== tabId),
      });

      await invoke('close_tab', { tabId });
    } catch (error) {
      console.error('Failed to close tab:', error);
    }
  },

  switchTab: async (tabId: string) => {
    try {
      set((state) => ({
        tabs: state.tabs.map(tab => ({
          ...tab,
          is_active: tab.id === tabId,
          last_accessed: tab.id === tabId ? Date.now() : tab.last_accessed,
        })),
        activeTabId: tabId,
      }));

      await invoke('switch_tab', { tabId });
    } catch (error) {
      console.error('Failed to switch tab:', error);
    }
  },

  updateTab: (tabId: string, updates: Partial<Tab>) => {
    set((state) => ({
      tabs: state.tabs.map(tab =>
        tab.id === tabId ? { ...tab, ...updates } : tab
      ),
    }));
  },

  updateTabs: async () => {
    try {
      const tabs = await invoke<Tab[]>('get_all_tabs');
      set({ tabs });
    } catch (error) {
      console.error('Failed to update tabs:', error);
    }
  },

  // Audio Detection
  setTabAudioState: (tabId: string, isPlaying: boolean) => {
    const { audioTabs } = get();
    
    set((state) => ({
      tabs: state.tabs.map(tab =>
        tab.id === tabId ? { ...tab, is_playing_audio: isPlaying } : tab
      ),
      audioTabs: isPlaying 
        ? [...audioTabs.filter(id => id !== tabId), tabId]
        : audioTabs.filter(id => id !== tabId),
    }));
  },

  getAudioTabs: () => get().audioTabs,

  // Smart Search & Navigation
  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
    if (query.length > 2) {
      get().getUrlCompletions(query);
    }
  },

  searchTabs: (query: string) => {
    const { tabs } = get();
    const lowercaseQuery = query.toLowerCase();
    
    return tabs
      .filter(tab => 
        tab.title.toLowerCase().includes(lowercaseQuery) ||
        tab.url.toLowerCase().includes(lowercaseQuery)
      )
      .sort((a, b) => b.last_accessed - a.last_accessed);
  },

  getUrlCompletions: async (input: string) => {
    try {
      const completions = await invoke<Array<{
        display: string;
        url: string;
        confidence: number;
      }>>('get_url_completions', { input });
      
      set({ urlCompletions: completions });
    } catch (error) {
      console.error('Failed to get URL completions:', error);
    }
  },

  navigateToUrl: async (url: string) => {
    try {
      const { activeTabId } = get();
      if (!activeTabId) {
        // Create new tab if none exists
        await get().createTab(url);
        return;
      }

      // Update tab state
      get().updateTab(activeTabId, {
        url,
        is_loading: true,
        title: 'Loading...',
        visit_count: (get().tabs.find(t => t.id === activeTabId)?.visit_count || 0) + 1,
      });

      // Call the correct Tauri backend command
      await invoke('navigate_to_url', { tab_id: activeTabId, url });
      console.log('✅ Navigated to:', url);
    } catch (error) {
      console.error('Failed to navigate:', error);
    }
  },

  // Voice Features
  toggleVoiceSearch: () => {
    set((state) => ({ isVoiceSearchActive: !state.isVoiceSearchActive }));
  },

  startVoiceRecognition: async () => {
    try {
      const result = await invoke<string>('start_voice_recognition');
      set({ searchQuery: result, isVoiceSearchActive: false });
      return result;
    } catch (error) {
      console.error('Voice recognition failed:', error);
      set({ isVoiceSearchActive: false });
    }
  },

  // Bookmarks & Intelligence
  suggestBookmark: (tabId: string) => {
    const tab = get().tabs.find(t => t.id === tabId);
    if (tab && !tab.bookmark_suggested) {
      get().updateTab(tabId, { bookmark_suggested: true });
      set((state) => ({
        bookmarkSuggestions: [...state.bookmarkSuggestions, tab.url],
      }));
    }
  },

  autoBookmark: async (tabId: string) => {
    try {
      const tab = get().tabs.find(t => t.id === tabId);
      if (!tab) return;

      await invoke('auto_bookmark', { 
        url: tab.url, 
        title: tab.title,
        visitCount: tab.visit_count,
      });

      get().updateTab(tabId, { bookmark_suggested: false });
    } catch (error) {
      console.error('Auto bookmark failed:', error);
    }
  },

  getBookmarkSuggestions: () => get().bookmarkSuggestions,

  // Chat Sidebar
  toggleChatSidebar: () => {
    set((state) => ({ isChatSidebarOpen: !state.isChatSidebarOpen }));
  },

  sendChatMessage: async (message: string) => {
    try {
      const response = await invoke<string>('send_chat_message', { message });
      return response;
    } catch (error) {
      console.error('Chat message failed:', error);
      return 'Sorry, I encountered an error. Please try again.';
    }
  },

  // Performance & Memory
  optimizeMemory: async () => {
    try {
      await invoke('optimize_memory');
    } catch (error) {
      console.error('Memory optimization failed:', error);
    }
  },

  preloadFrequentSites: async () => {
    try {
      await invoke('preload_frequent_sites');
    } catch (error) {
      console.error('Preloading failed:', error);
    }
  },
}));
