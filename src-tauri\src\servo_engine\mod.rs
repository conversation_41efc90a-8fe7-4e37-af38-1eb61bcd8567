use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::{Duration, Instant};

use anyhow::{Result, anyhow};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error, debug};

// Servo Core Imports
use servo::Servo;
use servo::config::opts::{self, Opts};
use servo::embedder_traits::{EmbedderProxy, EventLoopWaker, MediaSessionEvent};
use servo::servo_config::prefs::PrefValue;
use servo::servo_url::ServoUrl;
use servo::webrender_api::units::DeviceIntSize;
use servo::BrowserId;

// WebRender for GPU acceleration
use webrender::{RenderApi, Renderer, RendererOptions, Transaction};
use webrender_api::{DocumentId, PipelineId, RenderApiSender, units::*};
use surfman::{Context, ContextAttributeFlags, ContextAttributes, Device, GLVersion};

// Windowing and Events
use winit::{
    event::{Event, WindowEvent, KeyboardInput, VirtualKeyCode},
    event_loop::{ControlFlow, EventLoop},
    window::{Window, WindowBuilder},
    dpi::{LogicalSize, PhysicalSize},
};

pub mod compositor;
pub mod embedder;
pub mod networking;
pub mod security;
pub mod performance;

use crate::browser::Tab;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoConfig {
    pub enable_webgl: bool,
    pub enable_webgpu: bool,
    pub enable_media: bool,
    pub enable_canvas2d: bool,
    pub enable_experimental_features: bool,
    pub user_agent: String,
    pub enable_devtools: bool,
    pub enable_multiprocess: bool,
    pub memory_limit_mb: u64,
    pub cache_size_mb: u64,
    pub enable_hardware_acceleration: bool,
    pub enable_parallel_layout: bool,
    pub enable_parallel_styling: bool,
}

impl Default for ServoConfig {
    fn default() -> Self {
        Self {
            enable_webgl: true,
            enable_webgpu: true,
            enable_media: true,
            enable_canvas2d: true,
            enable_experimental_features: true,
            user_agent: "Memori/1.0 (Servo; AI-Enhanced)".to_string(),
            enable_devtools: cfg!(debug_assertions),
            enable_multiprocess: true,
            memory_limit_mb: 1024,
            cache_size_mb: 256,
            enable_hardware_acceleration: true,
            enable_parallel_layout: true,
            enable_parallel_styling: true,
        }
    }
}

#[derive(Debug, Clone)]
pub struct ServoTab {
    pub id: String,
    pub browser_id: BrowserId,
    pub pipeline_id: Option<PipelineId>,
    pub url: ServoUrl,
    pub title: String,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
    pub zoom_level: f32,
    pub is_muted: bool,
    pub security_state: SecurityState,
    pub performance_metrics: ServoPerformanceMetrics,
    pub window_size: DeviceIntSize,
    pub created_at: Instant,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityState {
    Secure,
    Insecure,
    Mixed,
    LocalFile,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoPerformanceMetrics {
    pub layout_time_ms: f64,
    pub paint_time_ms: f64,
    pub script_time_ms: f64,
    pub composite_time_ms: f64,
    pub memory_usage_mb: f64,
    pub frame_rate: f32,
    pub energy_impact: EnergyImpact,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnergyImpact {
    Low,
    Medium,
    High,
    VeryHigh,
}

impl Default for ServoPerformanceMetrics {
    fn default() -> Self {
        Self {
            layout_time_ms: 0.0,
            paint_time_ms: 0.0,
            script_time_ms: 0.0,
            composite_time_ms: 0.0,
            memory_usage_mb: 0.0,
            frame_rate: 60.0,
            energy_impact: EnergyImpact::Low,
        }
    }
}

pub struct ServoEngine {
    servo: Option<Servo<embedder::MemoriEmbedder>>,
    embedder: Arc<embedder::MemoriEmbedder>,
    config: ServoConfig,
    tabs: Arc<RwLock<HashMap<String, ServoTab>>>,
    active_tab_id: Arc<RwLock<Option<String>>>,
    render_api: Option<RenderApiSender>,
    event_loop_proxy: Option<winit::event_loop::EventLoopProxy<embedder::MemoriEvent>>,
    performance_monitor: Arc<performance::ServoPerformanceMonitor>,
    security_manager: Arc<security::ServoSecurityManager>,
}

impl ServoEngine {
    pub async fn new(config: ServoConfig) -> Result<Self> {
        info!("Initializing Servo engine with config: {:?}", config);

        // Create embedder
        let embedder = Arc::new(embedder::MemoriEmbedder::new(config.clone()).await?);
        
        // Initialize performance monitor
        let performance_monitor = Arc::new(performance::ServoPerformanceMonitor::new().await?);
        
        // Initialize security manager
        let security_manager = Arc::new(security::ServoSecurityManager::new(&config).await?);

        Ok(Self {
            servo: None,
            embedder,
            config,
            tabs: Arc::new(RwLock::new(HashMap::new())),
            active_tab_id: Arc::new(RwLock::new(None)),
            render_api: None,
            event_loop_proxy: None,
            performance_monitor,
            security_manager,
        })
    }

    pub async fn initialize(&mut self) -> Result<()> {
        info!("Starting Servo engine initialization");

        // Set up Servo options
        let mut opts = Opts::default();
        opts.url = Some(ServoUrl::parse("about:blank")?);
        opts.enable_webgl = self.config.enable_webgl;
        opts.enable_canvas2d = self.config.enable_canvas2d;
        opts.user_agent = Some(self.config.user_agent.clone());
        opts.enable_media = self.config.enable_media;
        opts.multiprocess = self.config.enable_multiprocess;
        opts.mem_profiler_period = Some(Duration::from_secs(5));
        opts.time_profiler_period = Some(Duration::from_secs(1));
        opts.enable_webdriver = self.config.enable_devtools;

        // Configure performance options
        if self.config.enable_parallel_layout {
            opts.layout_threads = Some(num_cpus::get());
        }

        // Set memory limits
        opts.memory_cache_size = Some(self.config.cache_size_mb * 1024 * 1024);

        // Initialize Servo with our embedder
        let servo = Servo::new(Arc::clone(&self.embedder), opts);
        self.servo = Some(servo);

        info!("Servo engine initialized successfully");
        Ok(())
    }

    pub async fn create_tab(&mut self, url: &str, title: Option<&str>) -> Result<String> {
        let tab_id = uuid::Uuid::new_v4().to_string();
        let servo_url = ServoUrl::parse(url)
            .map_err(|e| anyhow!("Invalid URL: {}", e))?;

        // Security check
        self.security_manager.validate_url(&servo_url).await?;

        // Create browser context
        let browser_id = if let Some(ref servo) = self.servo {
            servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::NewBrowser(
                servo_url.clone(),
                servo::BrowserId::new(),
            )]);
            servo::BrowserId::new()
        } else {
            return Err(anyhow!("Servo not initialized"));
        };

        let tab = ServoTab {
            id: tab_id.clone(),
            browser_id,
            pipeline_id: None,
            url: servo_url,
            title: title.unwrap_or("New Tab").to_string(),
            is_loading: true,
            can_go_back: false,
            can_go_forward: false,
            zoom_level: 1.0,
            is_muted: false,
            security_state: SecurityState::Unknown,
            performance_metrics: ServoPerformanceMetrics::default(),
            window_size: DeviceIntSize::new(1200, 800),
            created_at: Instant::now(),
        };

        // Store tab
        self.tabs.write().insert(tab_id.clone(), tab);
        
        // Set as active if first tab
        if self.active_tab_id.read().is_none() {
            *self.active_tab_id.write() = Some(tab_id.clone());
        }

        // Start performance monitoring
        self.performance_monitor.start_monitoring(&tab_id).await?;

        info!("Created Servo tab: {} with URL: {}", tab_id, url);
        Ok(tab_id)
    }

    pub async fn navigate(&mut self, tab_id: &str, url: &str) -> Result<()> {
        let servo_url = ServoUrl::parse(url)
            .map_err(|e| anyhow!("Invalid URL: {}", e))?;

        // Security validation
        self.security_manager.validate_url(&servo_url).await?;

        if let Some(tab) = self.tabs.write().get_mut(tab_id) {
            tab.url = servo_url.clone();
            tab.is_loading = true;
            tab.security_state = SecurityState::Unknown;

            // Send navigation command to Servo
            if let Some(ref servo) = self.servo {
                servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::LoadUrl(
                    tab.browser_id,
                    servo_url,
                )]);
            }

            info!("Navigating tab {} to: {}", tab_id, url);
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub async fn close_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.write().remove(tab_id) {
            // Close browser in Servo
            if let Some(ref servo) = self.servo {
                servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::CloseBrowser(
                    tab.browser_id,
                )]);
            }

            // Stop performance monitoring
            self.performance_monitor.stop_monitoring(tab_id).await?;

            // Update active tab if needed
            if self.active_tab_id.read().as_ref() == Some(&tab_id.to_string()) {
                let remaining_tabs: Vec<_> = self.tabs.read().keys().cloned().collect();
                *self.active_tab_id.write() = remaining_tabs.first().cloned();
            }

            info!("Closed Servo tab: {}", tab_id);
        }

        Ok(())
    }

    pub async fn switch_tab(&mut self, tab_id: &str) -> Result<()> {
        if self.tabs.read().contains_key(tab_id) {
            *self.active_tab_id.write() = Some(tab_id.to_string());
            
            // Focus the browser in Servo
            if let Some(tab) = self.tabs.read().get(tab_id) {
                if let Some(ref servo) = self.servo {
                    servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::Focus(
                        tab.browser_id,
                    )]);
                }
            }

            info!("Switched to tab: {}", tab_id);
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub async fn reload_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.read().get(tab_id) {
            if let Some(ref servo) = self.servo {
                servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::Reload(
                    tab.browser_id,
                )]);
            }
            info!("Reloading tab: {}", tab_id);
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub async fn go_back(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.read().get(tab_id) {
            if tab.can_go_back {
                if let Some(ref servo) = self.servo {
                    servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::GoBack(
                        tab.browser_id,
                    )]);
                }
                info!("Going back in tab: {}", tab_id);
            }
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub async fn go_forward(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.read().get(tab_id) {
            if tab.can_go_forward {
                if let Some(ref servo) = self.servo {
                    servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::GoForward(
                        tab.browser_id,
                    )]);
                }
                info!("Going forward in tab: {}", tab_id);
            }
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub async fn set_zoom(&mut self, tab_id: &str, zoom_level: f32) -> Result<()> {
        if let Some(mut tab) = self.tabs.write().get_mut(tab_id) {
            tab.zoom_level = zoom_level;
            
            if let Some(ref servo) = self.servo {
                servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::Zoom(
                    tab.browser_id,
                    zoom_level,
                )]);
            }
            
            info!("Set zoom level {:.2} for tab: {}", zoom_level, tab_id);
        } else {
            return Err(anyhow!("Tab not found: {}", tab_id));
        }

        Ok(())
    }

    pub fn get_tab(&self, tab_id: &str) -> Option<ServoTab> {
        self.tabs.read().get(tab_id).cloned()
    }

    pub fn get_all_tabs(&self) -> Vec<ServoTab> {
        self.tabs.read().values().cloned().collect()
    }

    pub fn get_active_tab_id(&self) -> Option<String> {
        self.active_tab_id.read().clone()
    }

    pub async fn update_tab_performance(&mut self, tab_id: &str, metrics: ServoPerformanceMetrics) -> Result<()> {
        if let Some(mut tab) = self.tabs.write().get_mut(tab_id) {
            tab.performance_metrics = metrics;
        }
        Ok(())
    }

    pub async fn run_event_loop(&mut self) -> Result<()> {
        info!("Starting Servo event loop");
        
        if let Some(ref mut servo) = self.servo {
            loop {
                // Process Servo events
                servo.handle_events(vec![]);
                
                // Small delay to prevent busy waiting
                tokio::time::sleep(Duration::from_millis(16)).await; // ~60 FPS
            }
        }

        Ok(())
    }

    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down Servo engine");

        // Close all tabs
        let tab_ids: Vec<_> = self.tabs.read().keys().cloned().collect();
        for tab_id in tab_ids {
            self.close_tab(&tab_id).await?;
        }

        // Shutdown Servo
        if let Some(servo) = self.servo.take() {
            servo.handle_events(vec![servo::embedder_traits::EmbedderMsg::Quit]);
        }

        info!("Servo engine shutdown complete");
        Ok(())
    }
}
